import { Page, Locator } from '@playwright/test';

export class UsersPage {
  readonly page: Page;
  readonly createUserButton: Locator;
  readonly userTable: Locator;
  readonly nameInput: Locator;
  readonly emailInput: Locator;
  readonly passwordInput: Locator;
  readonly roleSelect: Locator;
  readonly submitButton: Locator;
  readonly cancelButton: Locator;

  constructor(page: Page) {
    this.page = page;
    this.createUserButton = page.locator('text=Create User');
    this.userTable = page.locator('table');
    this.nameInput = page.locator('#name');
    this.emailInput = page.locator('#email');
    this.passwordInput = page.locator('#password');
    this.roleSelect = page.locator('#role');
    this.submitButton = page.locator('button[type="submit"]');
    this.cancelButton = page.locator('text=Cancel');
  }

  async goto() {
    await this.page.goto('/admin/users');
    await this.page.waitForLoadState('networkidle');
    // Wait for the users table to be visible
    await this.userTable.waitFor({ state: 'visible', timeout: 10000 });
  }

  async createUser(name: string, email: string, password: string, role: string = 'member') {
    await this.createUserButton.waitFor({ state: 'visible', timeout: 10000 });
    await this.createUserButton.click();

    // Wait for modal to open
    await this.nameInput.waitFor({ state: 'visible', timeout: 5000 });

    await this.nameInput.fill(name);
    await this.emailInput.fill(email);
    await this.passwordInput.fill(password);
    await this.roleSelect.selectOption(role);

    await this.submitButton.click();

    // Wait for modal to close and page to update
    await this.page.waitForLoadState('networkidle');
  }

  async editUser(userEmail: string, newName: string) {
    const userRow = this.page.locator(`tr:has-text("${userEmail}")`);
    await userRow.waitFor({ state: 'visible', timeout: 10000 });

    const editButton = userRow
      .locator('button:has-text("Edit"), [data-testid="edit-user"]')
      .first();
    await editButton.waitFor({ state: 'visible', timeout: 5000 });
    await editButton.click();

    // Wait for edit modal to open
    await this.nameInput.waitFor({ state: 'visible', timeout: 5000 });
    await this.nameInput.fill(newName);

    await this.submitButton.click();

    // Wait for modal to close and page to update
    await this.page.waitForLoadState('networkidle');
  }

  async deleteUser(userEmail: string) {
    const userRow = this.page.locator(`tr:has-text("${userEmail}")`);
    await userRow.waitFor({ state: 'visible', timeout: 10000 });

    const deleteButton = userRow
      .locator('button:has-text("Delete"), [data-testid="delete-user"]')
      .first();
    await deleteButton.waitFor({ state: 'visible', timeout: 5000 });
    await deleteButton.click();

    // Handle confirmation modal if it exists
    const confirmButton = this.page
      .locator('button:has-text("Delete User"), button:has-text("Confirm"), button:has-text("Yes")')
      .first();
    try {
      await confirmButton.waitFor({ state: 'visible', timeout: 3000 });
      await confirmButton.click();
    } catch (error) {
      // If no confirmation modal, the delete might have happened directly
      console.log('No confirmation modal found, delete may have completed');
    }

    // Wait for page to update
    await this.page.waitForLoadState('networkidle');
  }

  getUserRow(email: string) {
    return this.page.locator(`tr:has-text("${email}")`);
  }

  async getUserCount() {
    return await this.userTable.locator('tbody tr').count();
  }
}
