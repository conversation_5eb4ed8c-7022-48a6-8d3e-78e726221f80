'use client';

import React, { useEffect } from 'react';
import Link from 'next/link';
import { useCartCount } from '@/features/cart/hooks/useCart';
import { useAuth } from '@/features/auth/hooks/useAuth';
import { formatCartCount } from '@/shared/utils/formatNumber';

interface CartIconProps {
  className?: string;
  showCount?: boolean;
}

export const CartIcon: React.FC<CartIconProps> = ({ className = 'w-6 h-6', showCount = true }) => {
  const { user } = useAuth();
  const { count, refetch } = useCartCount();

  // Refresh cart count when component mounts or user changes
  useEffect(() => {
    if (user && user.role === 'member') {
      refetch();
    }
  }, [user, refetch]);

  // Listen for cart updates from other components
  useEffect(() => {
    const handleCartUpdate = () => {
      refetch();
    };

    // Listen for custom cart update events
    window.addEventListener('cartUpdated', handleCartUpdate);

    return () => {
      window.removeEventListener('cartUpdated', handleCartUpdate);
    };
  }, [refetch]);

  // Only show cart for members
  if (!user || user.role !== 'member') {
    return null;
  }

  return (
    <Link href="/cart" className="relative inline-block" data-testid="cart-icon">
      <svg
        className={`${className} text-gray-600 hover:text-gray-900 transition-colors`}
        fill="none"
        stroke="currentColor"
        viewBox="0 0 24 24"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 5M7 13l2.5 5m6-5v6a2 2 0 01-2 2H9a2 2 0 01-2-2v-6m8 0V9a2 2 0 00-2-2H9a2 2 0 00-2 2v4.01"
        />
      </svg>

      {showCount && count > 0 && (
        <span
          className="absolute -top-2 -right-2 bg-red-500 text-white text-xs font-bold rounded-full h-5 w-5 flex items-center justify-center min-w-[20px]"
          data-testid="cart-count"
        >
          {formatCartCount(count)}
        </span>
      )}
    </Link>
  );
};
