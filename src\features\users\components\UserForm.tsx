'use client';

import React, { useState } from 'react';
import { Button } from '@/shared/components/Button';
import { Input } from '@/shared/components/Input';
import { useUsers } from '../hooks/useUsers';
import { toast } from 'react-hot-toast';
import { User } from '../services/userService';

type UserFormProps = {
  user?: User;
  onSubmit: () => void;
  onCancel: () => void;
};

export const UserForm: React.FC<UserFormProps> = ({ user, onSubmit, onCancel }) => {
  const [name, setName] = useState(user?.name || '');
  const [email, setEmail] = useState(user?.email || '');
  const [password, setPassword] = useState('');
  const [role, setRole] = useState(user?.role || 'member');
  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const { createUser, updateUser } = useUsers();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setIsLoading(true);

    try {
      if (user) {
        // Update existing user
        const updateData: any = { name, email, role };
        if (password) {
          updateData.password = password;
        }
        await updateUser(user.id, updateData);
        toast.success(`${name} has been updated successfully.`);
      } else {
        // Create new user
        if (!password) {
          setError('Password is required for new users');
          return;
        }
        await createUser({ name, email, password, role });
        toast.success(`${name} has been created successfully.`);
      }
      onSubmit();
    } catch (err: any) {
      setError(err.message);
      toast.error(err.message || 'Operation failed');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit}>
      {error && <div className="mb-4 p-3 bg-red-100 text-red-700 rounded-md">{error}</div>}

      <Input id="name" label="Name" value={name} onChange={e => setName(e.target.value)} required />

      <Input
        id="email"
        label="Email"
        type="email"
        value={email}
        onChange={e => setEmail(e.target.value)}
        required
      />

      <Input
        id="password"
        label={user ? 'Password (leave blank to keep current)' : 'Password'}
        type="password"
        value={password}
        onChange={e => setPassword(e.target.value)}
        required={!user}
      />

      <div className="mb-4">
        <label htmlFor="role" className="block text-sm font-medium text-gray-700 mb-1">
          Role <span className="text-red-500">*</span>
        </label>
        <select
          id="role"
          value={role}
          onChange={e => setRole(e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          required
        >
          <option value="member">Member</option>
          <option value="admin">Admin</option>
        </select>
      </div>

      <div className="flex space-x-4">
        <Button type="submit" disabled={isLoading}>
          {isLoading ? 'Saving...' : user ? 'Update User' : 'Create User'}
        </Button>
        <Button type="button" variant="secondary" onClick={onCancel}>
          Cancel
        </Button>
      </div>
    </form>
  );
};
