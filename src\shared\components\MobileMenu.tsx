'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { useAuth } from '@/features/auth/hooks/useAuth';
import { Button } from './Button';

export const MobileMenu: React.FC = () => {
  const { user, logout, isAuthenticated, loading } = useAuth();
  const [isOpen, setIsOpen] = useState(false);

  if (loading || !isAuthenticated) return null;

  return (
    <div className="md:hidden">
      {/* Mobile menu button */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="p-2 rounded-md text-gray-700 hover:text-gray-900 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500"
      >
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          {isOpen ? (
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M6 18L18 6M6 6l12 12"
            />
          ) : (
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M4 6h16M4 12h16M4 18h16"
            />
          )}
        </svg>
      </button>

      {/* Mobile menu overlay */}
      {isOpen && (
        <>
          {/* Backdrop */}
          <div
            className="fixed inset-0 bg-black bg-opacity-50 z-40"
            onClick={() => setIsOpen(false)}
          />

          {/* Menu panel */}
          <div className="fixed top-16 right-0 w-64 h-[calc(100vh-4rem)] bg-white shadow-lg z-50 transform transition-transform">
            <div className="p-4 h-full overflow-y-auto">
              {/* Close button */}
              <div className="flex justify-end mb-4">
                <button
                  onClick={() => setIsOpen(false)}
                  className="p-2 rounded-md text-gray-700 hover:text-gray-900 hover:bg-gray-100"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M6 18L18 6M6 6l12 12"
                    />
                  </svg>
                </button>
              </div>

              {/* User info */}
              <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg mb-4">
                <div className="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center">
                  <span className="text-white font-medium">
                    {user?.name?.charAt(0).toUpperCase()}
                  </span>
                </div>
                <div>
                  <div className="text-sm font-medium text-gray-900">{user?.name}</div>
                  <div className="text-xs text-gray-500">{user?.email}</div>
                  <div className="text-xs text-gray-400">
                    {user?.role === 'admin' ? 'Administrator' : 'Member'}
                  </div>
                </div>
              </div>

              {/* Navigation links */}
              <nav className="space-y-2">
                <Link
                  href="/dashboard"
                  className="block px-3 py-2 rounded-md text-gray-700 hover:text-gray-900 hover:bg-gray-100 transition-colors"
                  onClick={() => setIsOpen(false)}
                >
                  Dashboard
                </Link>

                {user?.role === 'admin' && (
                  <Link
                    href="/admin/users"
                    className="block px-3 py-2 rounded-md text-gray-700 hover:text-gray-900 hover:bg-gray-100 transition-colors"
                    onClick={() => setIsOpen(false)}
                  >
                    Manage Users
                  </Link>
                )}
              </nav>

              {/* Logout button */}
              <div className="mt-6 pt-4 border-t border-gray-200">
                <Button
                  onClick={() => {
                    setIsOpen(false);
                    logout();
                  }}
                  variant="danger"
                  className="w-full"
                >
                  Logout
                </Button>
              </div>
            </div>
          </div>
        </>
      )}
    </div>
  );
};
