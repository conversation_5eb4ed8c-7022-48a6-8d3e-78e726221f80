import { NextRequest, NextResponse } from 'next/server';
import {
  getTransactions,
  createTransaction,
} from '@/features/transactions/services/transactionService';
import { getCartItems } from '@/features/cart/services/cartService';
import { getCurrentUser } from '@/features/auth/services/authService';

export async function GET(request: NextRequest) {
  try {
    const currentUser = await getCurrentUser();

    if (!currentUser) {
      return NextResponse.json({ message: 'Not authenticated' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');

    let userId: string | undefined;

    // Admin can see all transactions, others only their own
    if (currentUser.role !== 'admin') {
      userId = currentUser.id;
    }

    const result = await getTransactions(page, limit, userId);
    return NextResponse.json(result);
  } catch (error: any) {
    return NextResponse.json(
      { message: error.message || 'Failed to fetch transactions' },
      { status: 500 }
    );
  }
}

export async function POST() {
  try {
    const currentUser = await getCurrentUser();

    if (!currentUser) {
      return NextResponse.json({ message: 'Not authenticated' }, { status: 401 });
    }

    // Only members can checkout
    if (currentUser.role !== 'member' && currentUser.role !== 'admin') {
      return NextResponse.json({ message: 'Member access required to checkout' }, { status: 403 });
    }

    // Get current cart items
    const cartSummary = await getCartItems(currentUser.id);

    if (cartSummary.items.length === 0) {
      return NextResponse.json({ message: 'Cart is empty' }, { status: 400 });
    }

    // Check if all items are still available and active
    for (const item of cartSummary.items) {
      if (!item.product.isActive) {
        return NextResponse.json(
          { message: `Product "${item.product.name}" is no longer available` },
          { status: 400 }
        );
      }

      if (item.product.stock < item.quantity) {
        return NextResponse.json(
          {
            message: `Insufficient stock for "${item.product.name}". Available: ${item.product.stock}`,
          },
          { status: 400 }
        );
      }
    }

    // Create transaction
    const transactionData = {
      userId: currentUser.id,
      cartItems: cartSummary.items.map(item => ({
        productId: item.productId,
        quantity: item.quantity,
        price: item.product.price,
      })),
    };

    const transaction = await createTransaction(transactionData);
    return NextResponse.json(transaction, { status: 201 });
  } catch (error: any) {
    return NextResponse.json(
      { message: error.message || 'Failed to create transaction' },
      { status: 400 }
    );
  }
}
