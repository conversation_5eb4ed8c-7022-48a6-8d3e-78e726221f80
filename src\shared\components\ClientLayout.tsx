'use client';

import React from 'react';
import { useAuth } from '@/features/auth/hooks/useAuth';
import { UserDropdown } from './UserDropdown';
import { MobileMenu } from './MobileMenu';
import { Sidebar } from './Sidebar';
import { CartIcon } from './CartIcon';

type ClientLayoutProps = {
  children?: React.ReactNode;
  isNavigation?: boolean;
};

export const ClientLayout: React.FC<ClientLayoutProps> = ({ children, isNavigation = false }) => {
  const { isAuthenticated, loading } = useAuth();

  // If this is for navigation, only render navigation items
  if (isNavigation) {
    return (
      <div className="flex items-center space-x-6">
        {!loading && isAuthenticated ? (
          <>
            {/* Cart Icon */}
            <CartIcon className="w-6 h-6" />

            {/* Desktop User Dropdown */}
            <div className="hidden md:block">
              <UserDropdown />
            </div>

            {/* Mobile Menu */}
            <MobileMenu />
          </>
        ) : !loading ? (
          <>
            <a href="/login" className="text-gray-700 hover:text-gray-900 transition-colors">
              Login
            </a>
            <a href="/register" className="text-gray-700 hover:text-gray-900 transition-colors">
              Register
            </a>
          </>
        ) : null}
      </div>
    );
  }

  // Otherwise render the main content area with sidebar
  return (
    <div className="flex">
      {/* Left Sidebar - Only for authenticated users */}
      {!loading && isAuthenticated && (
        <div className="hidden md:block fixed left-0 top-16 h-[calc(100vh-4rem)] z-10">
          <Sidebar />
        </div>
      )}

      {/* Main Content */}
      <main className={`flex-1 ${!loading && isAuthenticated ? 'md:ml-64' : ''} p-6`}>
        <div className="max-w-7xl mx-auto">
          {loading ? (
            <div className="flex items-center justify-center min-h-[400px]">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
            </div>
          ) : (
            children
          )}
        </div>
      </main>
    </div>
  );
};
