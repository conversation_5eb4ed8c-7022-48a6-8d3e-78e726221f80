import Link from 'next/link';
import { Card } from '@/shared/components/Card';
import { Button } from '@/shared/components/Button';

export default function Home() {
  return (
    <div className="max-w-4xl mx-auto">
      <div className="text-center mb-12">
        <h1 className="text-4xl font-bold text-gray-900 mb-4">Welcome to FullStack Next.js App</h1>
        <p className="text-xl text-gray-600">
          A modern full-stack application with authentication and user management
        </p>
      </div>

      <div className="grid md:grid-cols-2 gap-8">
        <Card title="Authentication">
          <p className="text-gray-600 mb-4">
            Secure user authentication with login and registration functionality.
          </p>
          <div className="space-x-4">
            <Link href="/login">
              <Button>Login</Button>
            </Link>
            <Link href="/register">
              <Button variant="secondary">Register</Button>
            </Link>
          </div>
        </Card>

        <Card title="Product Catalog">
          <p className="text-gray-600 mb-4">
            Browse our extensive product catalog and add items to your cart.
          </p>
          <Link href="/products">
            <Button>Browse Products</Button>
          </Link>
        </Card>
      </div>

      <div className="mt-12 text-center">
        <Card>
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Tech Stack</h2>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div className="bg-blue-50 p-3 rounded">
              <strong>Frontend</strong>
              <br />
              Next.js 14
              <br />
              TypeScript
              <br />
              Tailwind CSS
            </div>
            <div className="bg-green-50 p-3 rounded">
              <strong>Backend</strong>
              <br />
              Next.js API
              <br />
              Prisma ORM
              <br />
              PostgreSQL
            </div>
            <div className="bg-purple-50 p-3 rounded">
              <strong>Auth</strong>
              <br />
              JWT Tokens
              <br />
              bcryptjs
              <br />
              HTTP-only cookies
            </div>
            <div className="bg-orange-50 p-3 rounded">
              <strong>Testing</strong>
              <br />
              Playwright
              <br />
              Page Object Model
              <br />
              E2E Tests
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
}
