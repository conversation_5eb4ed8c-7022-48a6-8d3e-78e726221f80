import { test, expect } from '@playwright/test';
import { DashboardPage } from '../../../pages/DashboardPage';

// Use member storage state for all tests in this file
test.use({ storageState: 'tests/.auth/member.json' });

test.describe('Member - Access Control', () => {
  let dashboardPage: DashboardPage;

  test.beforeEach(async ({ page }) => {
    dashboardPage = new DashboardPage(page);
  });

  test('should NOT have access to user management', async ({ page }) => {
    await dashboardPage.goto();

    // Member should not see "Manage Users" button or link
    const manageUsersButton = page.locator('a[href="/users"]');
    const manageUsersLink = page.locator('text="Manage Users"');
    const usersNavLink = page.locator('nav a:has-text("Users")');

    // Check that user management elements are not visible
    await expect(manageUsersButton).not.toBeVisible();
    await expect(manageUsersLink).not.toBeVisible();
    await expect(usersNavLink).not.toBeVisible();
  });

  test('should be redirected when trying to access users page directly', async ({ page }) => {
    // Try to navigate directly to users page
    await page.goto('/users');
    await page.waitForLoadState('networkidle');

    // Should see access denied message, not the users page
    await expect(page.locator('h3')).toContainText('Access Denied');

    // Should not see the users table or user management interface
    await expect(page.locator('h1:has-text("Users")')).not.toBeVisible();
    await expect(page.locator('[data-testid="user-table"]')).not.toBeVisible();
    await expect(page.locator('button:has-text("Create User")')).not.toBeVisible();
  });

  test('should only see member-appropriate navigation items', async ({ page }) => {
    await dashboardPage.goto();

    // Member should see these navigation items
    const productsLink = page.locator('nav a:has-text("Products")');
    const cartLink = page.locator('nav a:has-text("Cart")');
    const dashboardLink = page.locator('nav a:has-text("Dashboard")');

    await expect(productsLink).toBeVisible();
    await expect(cartLink).toBeVisible();
    await expect(dashboardLink).toBeVisible();

    // Member should NOT see admin-only navigation items
    const usersLink = page.locator('nav a:has-text("Users")');
    const adminProductsLink = page.locator('nav a:has-text("Admin Products")');
    const transactionsLink = page.locator('nav a:has-text("Transactions")');

    await expect(usersLink).not.toBeVisible();
    await expect(adminProductsLink).not.toBeVisible();
    await expect(transactionsLink).not.toBeVisible();
  });

  test('should display correct user role in dashboard', async ({ page }) => {
    await dashboardPage.goto();

    // Check that the user role is displayed as "member"
    const roleText = page
      .locator('text="Role:"')
      .locator('..')
      .or(page.locator('[data-testid="user-role"]'));

    await expect(roleText).toContainText('member');
  });

  test('should have access to shopping features', async ({ page }) => {
    await dashboardPage.goto();

    // Member should be able to access shopping-related pages
    await page.goto('/products');
    await expect(page.locator('h1')).toContainText('Products');

    await page.goto('/cart');
    await expect(page.locator('h1')).toContainText('Shopping Cart');
  });
});
