import { NextRequest, NextResponse } from 'next/server';
import { updateCartItemQuantity, removeFromCart } from '@/features/cart/services/cartService';
import { getCurrentUser } from '@/features/auth/services/authService';

export async function PUT(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    const currentUser = await getCurrentUser();

    if (!currentUser) {
      return NextResponse.json({ message: 'Not authenticated' }, { status: 401 });
    }

    const { quantity } = await request.json();
    const { id } = await params;

    if (quantity <= 0) {
      return NextResponse.json({ message: 'Quantity must be greater than 0' }, { status: 400 });
    }

    const cartItem = await updateCartItemQuantity(currentUser.id, id, quantity);
    return NextResponse.json(cartItem);
  } catch (error: any) {
    return NextResponse.json(
      { message: error.message || 'Failed to update cart item' },
      { status: 400 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const currentUser = await getCurrentUser();

    if (!currentUser) {
      return NextResponse.json({ message: 'Not authenticated' }, { status: 401 });
    }

    const { id } = await params;
    await removeFromCart(currentUser.id, id);

    return NextResponse.json({ message: 'Item removed from cart' });
  } catch (error: any) {
    return NextResponse.json(
      { message: error.message || 'Failed to remove cart item' },
      { status: 400 }
    );
  }
}
