'use client';

import { useAuth } from '@/features/auth/hooks/useAuth';
import { Card } from '@/shared/components/Card';
import Link from 'next/link';
import { Button } from '@/shared/components/Button';

export default function DashboardPage() {
  const { user, loading } = useAuth();

  if (loading) {
    return <div className="text-center">Loading...</div>;
  }

  if (!user) {
    return (
      <div className="text-center">
        <p className="text-gray-600 mb-4">Please log in to access the dashboard.</p>
        <Link href="/login">
          <Button>Login</Button>
        </Link>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto">
      <h1 className="text-3xl font-bold text-gray-900 mb-8">Dashboard</h1>

      <div className="grid md:grid-cols-2 gap-6">
        <Card title="Welcome Back!">
          <p className="text-gray-600 mb-2">
            <strong>Name:</strong> {user.name}
          </p>
          <p className="text-gray-600 mb-2">
            <strong>Email:</strong> {user.email}
          </p>
          <p className="text-gray-600">
            <strong>Role:</strong>
            <span
              className={`ml-2 px-2 py-1 text-xs rounded-full ${
                user.role === 'admin'
                  ? 'bg-purple-100 text-purple-800'
                  : 'bg-gray-100 text-gray-800'
              }`}
            >
              {user.role}
            </span>
          </p>
        </Card>

        <Card title="Quick Actions">
          <div className="space-y-3">
            {user.role === 'admin' && (
              <>
                <Link href="/admin/users" className="block">
                  <Button className="w-full">Manage Users</Button>
                </Link>
                <p className="text-sm text-gray-600">
                  As an admin, you can create, edit, and delete users.
                </p>
              </>
            )}
            {user.role === 'member' && (
              <>
                <Link href="/products" className="block">
                  <Button className="w-full">Browse Products</Button>
                </Link>
                <p className="text-sm text-gray-600">
                  Explore our product catalog and add items to your cart.
                </p>
              </>
            )}
          </div>
        </Card>
      </div>
    </div>
  );
}
