import { Page, Locator } from '@playwright/test';

export class DashboardPage {
  readonly page: Page;
  readonly welcomeCard: Locator;
  readonly userName: Locator;
  readonly userEmail: Locator;
  readonly userRole: Locator;
  readonly manageUsersButton: Locator;
  readonly logoutButton: Locator;

  constructor(page: Page) {
    this.page = page;
    this.welcomeCard = page
      .locator('h2:has-text("Welcome Back!"), h3:has-text("Welcome Back!")')
      .first();
    this.userName = page.locator('text=Name:').locator('..');
    this.userEmail = page.locator('text=Email:').locator('..');
    this.userRole = page.locator('text=Role:').locator('..');
    this.manageUsersButton = page
      .locator('a[href="/users"], button:has-text("Manage Users")')
      .first();
    this.logoutButton = page
      .locator('button:has-text("Logout"), button:has-text("Sign out")')
      .first();
  }

  async goto() {
    await this.page.goto('/dashboard');
    await this.page.waitForLoadState('networkidle');
    // Wait for welcome card to be visible
    await this.welcomeCard.waitFor({ state: 'visible', timeout: 10000 });
  }

  async logout() {
    try {
      // Try to find user dropdown button with various selectors
      const userDropdown = this.page
        .locator(
          'button:has-text("Admin"), button:has-text("User"), [data-testid="user-menu"], .user-dropdown'
        )
        .first();
      await userDropdown.waitFor({ state: 'visible', timeout: 5000 });
      await userDropdown.click();

      // Wait for dropdown to open and click logout
      await this.logoutButton.waitFor({ state: 'visible', timeout: 5000 });
      await this.logoutButton.click();
    } catch (error) {
      // Fallback: try direct logout button
      await this.logoutButton.click();
    }

    await this.page.waitForLoadState('networkidle');
  }

  async goToUsers() {
    await this.manageUsersButton.waitFor({ state: 'visible', timeout: 10000 });
    await this.manageUsersButton.click();
    await this.page.waitForLoadState('networkidle');
  }

  async getUserInfo() {
    const name = await this.userName.textContent();
    const email = await this.userEmail.textContent();
    const role = await this.userRole.textContent();

    return { name, email, role };
  }
}
