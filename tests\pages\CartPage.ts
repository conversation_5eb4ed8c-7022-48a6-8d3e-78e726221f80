import { Page, Locator } from '@playwright/test';

export class CartPage {
  readonly page: Page;
  readonly cartItems: Locator;
  readonly emptyCartMessage: Locator;
  readonly checkoutButton: Locator;
  readonly clearCartButton: Locator;
  readonly continueShoppingButton: Locator;
  readonly totalAmount: Locator;
  readonly itemCount: Locator;

  constructor(page: Page) {
    this.page = page;
    this.cartItems = page.locator('[data-testid="cart-item"]');
    this.emptyCartMessage = page.locator('h3:has-text("Your cart is empty")');
    this.checkoutButton = page.locator('button:has-text("Checkout")');
    this.clearCartButton = page.locator('button:has-text("Clear Cart")');
    this.continueShoppingButton = page.locator('a:has-text("Continue Shopping")');
    this.totalAmount = page
      .locator('[data-testid="total-amount"]')
      .or(page.locator('text=/Total.*\\$\\d+\\.\\d{2}/'));
    this.itemCount = page
      .locator('[data-testid="item-count"]')
      .or(page.locator('text=/\\d+ item/'));
  }

  async goto() {
    await this.page.goto('/cart');
    await this.page.waitForLoadState('networkidle');
  }

  async increaseQuantity(itemIndex: number) {
    const cartItem = this.cartItems.nth(itemIndex);
    // Find all buttons in the cart item and get the one with + text
    const buttons = cartItem.locator('button');
    const buttonCount = await buttons.count();

    // Look for the + button by checking text content
    let increaseButton = null;
    for (let i = 0; i < buttonCount; i++) {
      const button = buttons.nth(i);
      const text = await button.textContent();
      if (text?.trim() === '+') {
        increaseButton = button;
        break;
      }
    }

    if (increaseButton) {
      await increaseButton.click();
    } else {
      throw new Error('Could not find + button');
    }

    // Wait for quantity update request
    try {
      await this.page.waitForResponse(
        response => response.url().includes('/api/cart/') && response.request().method() === 'PUT',
        { timeout: 5000 }
      );
    } catch (error) {
      // If no API response, wait for UI update
      await this.page.waitForLoadState('networkidle');
    }
  }

  async decreaseQuantity(itemIndex: number) {
    const cartItem = this.cartItems.nth(itemIndex);
    // Find all buttons in the cart item and get the one with - text
    const buttons = cartItem.locator('button');
    const buttonCount = await buttons.count();

    // Look for the - button by checking text content
    let decreaseButton = null;
    for (let i = 0; i < buttonCount; i++) {
      const button = buttons.nth(i);
      const text = await button.textContent();
      if (text?.trim() === '-') {
        decreaseButton = button;
        break;
      }
    }

    if (decreaseButton) {
      await decreaseButton.click();
    } else {
      throw new Error('Could not find - button');
    }

    // Wait for quantity update request
    try {
      await this.page.waitForResponse(
        response => response.url().includes('/api/cart/') && response.request().method() === 'PUT',
        { timeout: 5000 }
      );
    } catch (error) {
      // If no API response, wait for UI update
      await this.page.waitForLoadState('networkidle');
    }
  }

  async removeItem(itemIndex: number) {
    const cartItem = this.cartItems.nth(itemIndex);

    // Try multiple selectors for the remove button
    const removeButton = cartItem
      .locator('button[data-testid="remove-item"]')
      .or(cartItem.locator('button:has-text("Remove")'))
      .or(cartItem.locator('button:has([data-testid="trash-icon"])'))
      .or(cartItem.locator('button.text-red-600'))
      .or(cartItem.locator('button').last());

    await removeButton.click();

    // Wait for remove request
    try {
      await this.page.waitForResponse(
        response =>
          response.url().includes('/api/cart/') && response.request().method() === 'DELETE',
        { timeout: 5000 }
      );
    } catch (error) {
      // If no API response, wait for UI update
      await this.page.waitForLoadState('networkidle');
    }
  }

  async getQuantity(itemIndex: number): Promise<Locator> {
    const cartItem = this.cartItems.nth(itemIndex);
    return cartItem.locator('[data-testid="quantity"]');
  }

  async getItemName(itemIndex: number): Promise<string> {
    const cartItem = this.cartItems.nth(itemIndex);
    const nameElement = cartItem.locator('h3').first();
    return (await nameElement.textContent()) || '';
  }

  async getItemPrice(itemIndex: number): Promise<string> {
    const cartItem = this.cartItems.nth(itemIndex);
    const priceElement = cartItem.locator('text=/\\$\\d+\\.\\d{2}.*each/').first();
    const priceText = (await priceElement.textContent()) || '$0.00 each';
    return priceText.replace(' each', '').replace('$', '');
  }

  async getItemSubtotal(itemIndex: number): Promise<string> {
    const cartItem = this.cartItems.nth(itemIndex);
    const subtotalElement = cartItem.locator('text=/\\$\\d+\\.\\d{2}/').last();
    const subtotalText = (await subtotalElement.textContent()) || '$0.00';
    return subtotalText.replace('$', '');
  }

  async getTotalAmount(): Promise<string> {
    const totalElement = this.page
      .locator('text=/Total.*\\$\\d+\\.\\d{2}/')
      .or(this.page.locator('[data-testid="total-amount"]'));
    const totalText = (await totalElement.textContent()) || '$0.00';
    const match = totalText.match(/\$(\d+\.\d{2})/);
    return match ? match[1] : '0.00';
  }

  async getItemCount(): Promise<number> {
    const countElement = this.page.locator('text=/\\d+ item/').first();
    const countText = (await countElement.textContent()) || '0 items';
    const match = countText.match(/(\d+) item/);
    return match ? parseInt(match[1]) : 0;
  }

  async checkout() {
    await this.checkoutButton.click();

    // Wait for checkout request
    try {
      await this.page.waitForResponse(
        response =>
          response.url().includes('/api/transactions') && response.request().method() === 'POST',
        { timeout: 10000 }
      );
    } catch (error) {
      console.log('Checkout API response not detected, continuing...');
    }

    // Wait for UI to update (either success or error)
    await this.page.waitForLoadState('networkidle');
  }

  async clearCart() {
    // Check if cart is already empty
    if (await this.isCartEmpty()) {
      return;
    }

    // Use API approach for more reliable cart clearing
    await this.page.evaluate(async () => {
      try {
        const response = await fetch('/api/cart', {
          method: 'DELETE',
          headers: {
            'Content-Type': 'application/json',
          },
        });
        return response.ok;
      } catch (error) {
        console.log('Cart clear API failed:', error);
        return false;
      }
    });

    // Wait for UI to update
    await this.page.waitForLoadState('networkidle');

    // Refresh the page to ensure cart is cleared
    await this.page.reload();
    await this.page.waitForLoadState('networkidle');
  }

  async continueShopping() {
    await this.continueShoppingButton.click();
    await this.page.waitForURL('**/products');
  }

  async isCartEmpty(): Promise<boolean> {
    try {
      // Check if empty cart message is visible
      const emptyMessageVisible = await this.emptyCartMessage.isVisible({ timeout: 2000 });
      if (emptyMessageVisible) return true;

      // Alternative check: see if cart items count is 0
      const itemCount = await this.cartItems.count();
      return itemCount === 0;
    } catch (error) {
      // Fallback: check if cart items exist
      const itemCount = await this.cartItems.count();
      return itemCount === 0;
    }
  }

  async isCheckoutDisabled(): Promise<boolean> {
    if (await this.checkoutButton.isVisible()) {
      return await this.checkoutButton.isDisabled();
    }
    return true; // If button is not visible, consider it disabled
  }

  async hasUnavailableItems(): Promise<boolean> {
    const warningMessage = this.page.locator('text="Items need attention"');
    return await warningMessage.isVisible();
  }

  async hasInactiveProductWarning(itemIndex: number): Promise<boolean> {
    const cartItem = this.cartItems.nth(itemIndex);
    const warningElement = cartItem.locator('text="This product is no longer available"');
    return await warningElement.isVisible();
  }

  async hasStockWarning(itemIndex: number): Promise<boolean> {
    const cartItem = this.cartItems.nth(itemIndex);
    const warningElement = cartItem.locator('text=/Only \\d+ left in stock/');
    return await warningElement.isVisible();
  }

  async getStockWarningText(itemIndex: number): Promise<string> {
    const cartItem = this.cartItems.nth(itemIndex);
    const warningElement = cartItem.locator('text=/Only \\d+ left in stock/');
    return (await warningElement.textContent()) || '';
  }

  async isQuantityButtonDisabled(
    itemIndex: number,
    button: 'increase' | 'decrease'
  ): Promise<boolean> {
    const cartItem = this.cartItems.nth(itemIndex);
    const buttonElement =
      button === 'increase'
        ? cartItem.locator('button:has-text("+")')
        : cartItem.locator('button:has-text("-")');

    return await buttonElement.isDisabled();
  }

  async waitForCartUpdate() {
    // Wait for any cart-related API call to complete
    try {
      await this.page.waitForResponse(response => response.url().includes('/api/cart'), {
        timeout: 5000,
      });
    } catch (error) {
      // If no API call detected, wait for network idle
      await this.page.waitForLoadState('networkidle');
    }
  }

  async getCartSummary(): Promise<{
    itemCount: number;
    totalAmount: string;
    hasWarnings: boolean;
  }> {
    const itemCount = await this.getItemCount();
    const totalAmount = await this.getTotalAmount();
    const hasWarnings = await this.hasUnavailableItems();

    return {
      itemCount,
      totalAmount,
      hasWarnings,
    };
  }

  async verifySecurityNotice(): Promise<boolean> {
    const securityNotice = this.page.locator('text="Secure checkout powered by SSL encryption"');
    return await securityNotice.isVisible();
  }

  async getShippingInfo(): Promise<string> {
    const shippingElement = this.page
      .locator('text="Shipping"')
      .locator('..')
      .locator('span')
      .last();
    return (await shippingElement.textContent()) || '';
  }

  async getTaxInfo(): Promise<string> {
    const taxElement = this.page.locator('text="Tax"').locator('..').locator('span').last();
    return (await taxElement.textContent()) || '';
  }
}
