const { chromium } = require('@playwright/test');
const path = require('path');

/**
 * Global authentication setup for Playwright tests
 * This script logs in as different user roles and saves their authentication state
 * to be reused across tests, eliminating the need for repeated login operations.
 */

const ADMIN_STORAGE_STATE = path.join(__dirname, '../.auth/admin.json');
const MEMBER_STORAGE_STATE = path.join(__dirname, '../.auth/member.json');

async function globalSetup() {
  console.log('🔐 Starting global authentication setup...');

  const browser = await chromium.launch();

  try {
    // Setup Admin Authentication
    console.log('👑 Setting up admin authentication...');
    await setupAdminAuth(browser);

    // Setup Member Authentication
    console.log('👤 Setting up member authentication...');
    await setupMemberAuth(browser);

    console.log('✅ Global authentication setup completed successfully!');
  } catch (error) {
    console.error('❌ Global authentication setup failed:', error);
    throw error;
  } finally {
    await browser.close();
  }
}

async function setupAdminAuth(browser) {
  const context = await browser.newContext();
  const page = await context.newPage();

  try {
    // Navigate to login page with retry mechanism
    const baseUrl = process.env.BASE_URL || 'http://localhost:3000';
    console.log(`🔗 Navigating to login page: ${baseUrl}/login`);

    await page.goto(`${baseUrl}/login`, { waitUntil: 'networkidle' });

    // Wait for page to be fully loaded
    await page.waitForLoadState('domcontentloaded');

    // Wait for login form to be visible (increased timeout for CI)
    console.log('⏳ Waiting for email input field...');
    await page.waitForSelector('input[type="email"]', { timeout: 30000 });

    // Fill login form with admin credentials
    console.log('📝 Filling admin credentials...');
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'password123');

    // Submit login form
    console.log('🚀 Submitting login form...');
    await page.click('button[type="submit"]');

    // Wait for successful login and redirect to dashboard (increased timeout for CI)
    console.log('⏳ Waiting for redirect to dashboard...');
    await page.waitForURL('**/dashboard', { timeout: 30000 });

    // Verify admin is logged in by checking for admin-specific elements
    console.log('🔍 Verifying admin user is logged in...');
    await page.waitForSelector('text=Admin User', { timeout: 15000 });

    // Save authentication state
    await context.storageState({ path: ADMIN_STORAGE_STATE });
    console.log('✅ Admin authentication state saved');
  } catch (error) {
    console.error('❌ Failed to setup admin authentication:', error);

    // Capture page content for debugging
    try {
      const url = page.url();
      const title = await page.title();
      console.error(`📍 Current URL: ${url}`);
      console.error(`📄 Page title: ${title}`);

      // Take screenshot for debugging
      await page.screenshot({ path: 'admin-auth-error.png', fullPage: true });
      console.error('📸 Screenshot saved as admin-auth-error.png');
    } catch (debugError) {
      console.error('Failed to capture debug info:', debugError);
    }

    throw error;
  } finally {
    await context.close();
  }
}

async function setupMemberAuth(browser) {
  const context = await browser.newContext();
  const page = await context.newPage();

  try {
    // Navigate to login page with retry mechanism
    const baseUrl = process.env.BASE_URL || 'http://localhost:3000';
    console.log(`🔗 Navigating to login page: ${baseUrl}/login`);

    await page.goto(`${baseUrl}/login`, { waitUntil: 'networkidle' });

    // Wait for page to be fully loaded
    await page.waitForLoadState('domcontentloaded');

    // Wait for login form to be visible (increased timeout for CI)
    console.log('⏳ Waiting for email input field...');
    await page.waitForSelector('input[type="email"]', { timeout: 30000 });

    // Fill login form with member credentials
    console.log('📝 Filling member credentials...');
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'password123');

    // Submit login form
    console.log('🚀 Submitting login form...');
    await page.click('button[type="submit"]');

    // Wait for successful login and redirect to dashboard (increased timeout for CI)
    console.log('⏳ Waiting for redirect to dashboard...');
    await page.waitForURL('**/dashboard', { timeout: 30000 });

    // Verify member is logged in by checking for member-specific elements
    console.log('🔍 Verifying member user is logged in...');
    await page.waitForSelector('text=Member User', { timeout: 15000 });

    // Save authentication state
    await context.storageState({ path: MEMBER_STORAGE_STATE });
    console.log('✅ Member authentication state saved');
  } catch (error) {
    console.error('❌ Failed to setup member authentication:', error);

    // Capture page content for debugging
    try {
      const url = page.url();
      const title = await page.title();
      console.error(`📍 Current URL: ${url}`);
      console.error(`📄 Page title: ${title}`);

      // Take screenshot for debugging
      await page.screenshot({ path: 'member-auth-error.png', fullPage: true });
      console.error('📸 Screenshot saved as member-auth-error.png');
    } catch (debugError) {
      console.error('Failed to capture debug info:', debugError);
    }

    throw error;
  } finally {
    await context.close();
  }
}

module.exports = globalSetup;
