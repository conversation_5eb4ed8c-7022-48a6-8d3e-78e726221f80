import { test, expect } from '@playwright/test';
import { RegisterPage } from '../../pages/RegisterPage';
import { DashboardPage } from '../../pages/DashboardPage';
import { LoginPage } from '../../pages/LoginPage';

test.describe('Authentication - Unauthenticated Users', () => {
  test('should register a new user successfully', async ({ page }) => {
    const registerPage = new RegisterPage(page);
    const dashboardPage = new DashboardPage(page);

    await registerPage.goto();

    const timestamp = Date.now();
    const testEmail = `test${timestamp}@example.com`;

    await registerPage.register('Test User', testEmail, 'password123', 'password123');

    // Should redirect to dashboard after successful registration
    await expect(page).toHaveURL('/dashboard', { timeout: 15000 });
    await expect(dashboardPage.welcomeCard).toBeVisible({ timeout: 10000 });
  });

  test('should show error for mismatched passwords during registration', async ({ page }) => {
    const registerPage = new RegisterPage(page);

    await registerPage.goto();

    await registerPage.register(
      'Test User',
      '<EMAIL>',
      'password123',
      'differentpassword'
    );

    const errorMessage = await registerPage.getErrorMessage();
    expect(errorMessage).toContain('Passwords do not match');
  });

  test('should login with valid credentials', async ({ page }) => {
    const loginPage = new LoginPage(page);
    const dashboardPage = new DashboardPage(page);

    await loginPage.goto();

    // Use seeded admin user
    await loginPage.login('<EMAIL>', 'password123');

    // Should redirect to dashboard after successful login
    await expect(page).toHaveURL('/dashboard', { timeout: 15000 });
    await expect(dashboardPage.welcomeCard).toBeVisible({ timeout: 10000 });
  });

  test('should show error for invalid credentials', async ({ page }) => {
    const loginPage = new LoginPage(page);

    await loginPage.goto();

    await loginPage.login('<EMAIL>', 'wrongpassword');

    const errorMessage = await loginPage.getErrorMessage();
    expect(errorMessage).toContain('Invalid email or password');
  });

  test('should logout successfully', async ({ page }) => {
    const loginPage = new LoginPage(page);
    const dashboardPage = new DashboardPage(page);

    // Login first
    await loginPage.goto();
    await loginPage.login('<EMAIL>', 'password123');
    await expect(page).toHaveURL('/dashboard');

    // Logout
    await dashboardPage.logout();
    await expect(page).toHaveURL('/login');
  });

  test('should navigate between login and register pages', async ({ page }) => {
    const loginPage = new LoginPage(page);
    const registerPage = new RegisterPage(page);

    await loginPage.goto();
    await loginPage.goToRegister();
    await expect(page).toHaveURL('/register');

    await registerPage.goToLogin();
    await expect(page).toHaveURL('/login');
  });
});
