/**
 * Utility functions for formatting numbers with proper comma separators
 */

/**
 * Format a number with comma separators for thousands
 * @param num - The number to format
 * @returns Formatted string with commas (e.g., 1,234)
 */
export function formatNumber(num: number): string {
  return num.toLocaleString('en-US');
}

/**
 * Format a price with comma separators and currency symbol
 * @param price - The price to format
 * @param currency - Currency symbol (default: '$')
 * @returns Formatted price string (e.g., $1,234.56)
 */
export function formatPrice(price: number, currency: string = '$'): string {
  return `${currency}${price.toLocaleString('en-US', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  })}`;
}

/**
 * Format a quantity with comma separators
 * @param quantity - The quantity to format
 * @returns Formatted quantity string (e.g., 1,234)
 */
export function formatQuantity(quantity: number): string {
  return quantity.toLocaleString('en-US');
}

/**
 * Format cart count with special handling for large numbers
 * @param count - The count to format
 * @param maxDisplay - Maximum number to display before showing "+" (default: 99)
 * @returns Formatted count string (e.g., "1,234" or "99+")
 */
export function formatCartCount(count: number, maxDisplay: number = 99): string {
  if (count > maxDisplay) {
    return `${formatNumber(maxDisplay)}+`;
  }
  return formatNumber(count);
}

/**
 * Format stock count with comma separators
 * @param stock - The stock count to format
 * @returns Formatted stock string (e.g., "1,234 in stock")
 */
export function formatStock(stock: number): string {
  return `${formatNumber(stock)} in stock`;
}

/**
 * Format item count with proper pluralization
 * @param count - The item count
 * @param singular - Singular form (default: 'item')
 * @param plural - Plural form (default: 'items')
 * @returns Formatted string (e.g., "1 item" or "1,234 items")
 */
export function formatItemCount(
  count: number,
  singular: string = 'item',
  plural: string = 'items'
): string {
  const formattedCount = formatNumber(count);
  return `${formattedCount} ${count === 1 ? singular : plural}`;
}
