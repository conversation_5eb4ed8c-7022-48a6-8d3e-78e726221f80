import { defineConfig, devices } from '@playwright/test';

export default defineConfig({
  fullyParallel: true,
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 1,
  workers: 2,
  timeout: 90000,
  expect: {
    timeout: 15000,
  },
  reporter: process.env.CI
    ? [['html'], ['github'], ['json', { outputFile: 'test-results.json' }]]
    : [['html'], ['list']],
  globalSetup: './tests/config/auth.setup.js',
  use: {
    baseURL: process.env.BASE_URL ?? 'http://localhost:3000',
    trace: 'on-first-retry',
    screenshot: 'only-on-failure',
    video: 'retain-on-failure',
    actionTimeout: 20000, // Increase action timeout
    navigationTimeout: 30000, // Increase navigation timeout
    headless: true,
  },

  projects: [
    // Setup project to generate authentication states (run manually)
    {
      name: 'setup',
      testMatch: '**/config/auth.setup.test.js',
      use: { ...devices['Desktop Chrome'] },
    },

    // Unauthenticated tests (no storage state needed)
    {
      name: 'unauthenticated',
      testDir: './tests/roles/unauthenticated',
      use: { ...devices['Desktop Chrome'] },
    },

    // Admin role tests (use admin storage state)
    {
      name: 'admin',
      testDir: './tests/roles/admin',
      use: {
        ...devices['Desktop Chrome'],
        storageState: 'tests/.auth/admin.json',
      },
    },

    // Member role tests (use member storage state)
    {
      name: 'member',
      testDir: './tests/roles/member',
      use: {
        ...devices['Desktop Chrome'],
        storageState: 'tests/.auth/member.json',
      },
    },
  ],

  // webServer: {
  //   command: 'npm run dev',
  //   url: process.env.BASE_URL ?? 'http://localhost:3000',
  //   reuseExistingServer: !process.env.CI,
  //   timeout: 120 * 1000,
  //   stdout: 'pipe',
  //   stderr: 'pipe',
  // },
});
