'use client';

import React, { useState } from 'react';
import { CartSummary as CartSummaryType } from '../services/cartService';
import { Button } from '@/shared/components/Button';
import { ConfirmationModal } from '@/shared/components/ConfirmationModal';
import { toast } from 'react-hot-toast';
import { formatPrice, formatQuantity } from '@/shared/utils/formatNumber';

interface CartSummaryProps {
  cart: CartSummaryType;
  onCheckout: () => Promise<any>;
  onClearCart: () => Promise<void>;
}

export const CartSummary: React.FC<CartSummaryProps> = ({ cart, onCheckout, onClearCart }) => {
  const [isCheckingOut, setIsCheckingOut] = useState(false);
  const [isClearingCart, setIsClearingCart] = useState(false);
  const [showClearConfirmation, setShowClearConfirmation] = useState(false);

  const hasUnavailableItems = cart.items.some(
    item => !item.product.isActive || item.product.stock < item.quantity
  );

  const handleCheckout = async () => {
    if (hasUnavailableItems) {
      toast.error('Please remove unavailable items before checkout');
      return;
    }

    try {
      setIsCheckingOut(true);
      const transaction = await onCheckout();
      toast.success(`Order placed successfully! Order ID: ${transaction.id}`);
    } catch (error: any) {
      toast.error(error.message || 'Checkout failed');
    } finally {
      setIsCheckingOut(false);
    }
  };

  const handleClearCart = () => {
    setShowClearConfirmation(true);
  };

  const handleClearConfirm = async () => {
    try {
      setIsClearingCart(true);
      setShowClearConfirmation(false);
      await onClearCart();
      toast.success('Cart cleared successfully');
    } catch (error: any) {
      toast.error(error.message || 'Failed to clear cart');
    } finally {
      setIsClearingCart(false);
    }
  };

  const handleClearCancel = () => {
    setShowClearConfirmation(false);
  };

  if (cart.items.length === 0) {
    return (
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <div className="text-center py-8">
          <div className="text-gray-400 mb-4">
            <svg
              className="mx-auto h-16 w-16"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 5M7 13l2.5 5m6-5v6a2 2 0 01-2 2H9a2 2 0 01-2-2v-6m8 0V9a2 2 0 00-2-2H9a2 2 0 00-2 2v4.01"
              />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">Your cart is empty</h3>
          <p className="text-gray-600 mb-6">Add some products to get started!</p>
          <Button as="a" href="/products">
            Browse Products
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border p-6">
      <h2 className="text-xl font-semibold text-gray-900 mb-6">Order Summary</h2>

      {/* Cart Items Summary */}
      <div className="space-y-3 mb-6">
        <div className="flex justify-between text-sm">
          <span className="text-gray-600">Items ({formatQuantity(cart.totalItems)})</span>
          <span className="text-gray-900">{formatPrice(cart.totalAmount)}</span>
        </div>

        <div className="flex justify-between text-sm">
          <span className="text-gray-600">Shipping</span>
          <span className="text-green-600">Free</span>
        </div>

        <div className="flex justify-between text-sm">
          <span className="text-gray-600">Tax</span>
          <span className="text-gray-900">Calculated at checkout</span>
        </div>

        <div className="border-t pt-3">
          <div className="flex justify-between text-lg font-semibold">
            <span className="text-gray-900">Total</span>
            <span className="text-gray-900">{formatPrice(cart.totalAmount)}</span>
          </div>
        </div>
      </div>

      {/* Warnings */}
      {hasUnavailableItems && (
        <div className="bg-orange-50 border border-orange-200 rounded-md p-4 mb-6">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg
                className="h-5 w-5 text-orange-400"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-orange-800">Items need attention</h3>
              <div className="mt-2 text-sm text-orange-700">
                <p>
                  Some items in your cart are unavailable or have limited stock. Please review and
                  update your cart before checkout.
                </p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Action Buttons */}
      <div className="space-y-3">
        <Button
          onClick={handleCheckout}
          disabled={isCheckingOut || hasUnavailableItems || cart.items.length === 0}
          className="w-full"
        >
          {isCheckingOut ? (
            <div className="flex items-center justify-center">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              Processing...
            </div>
          ) : (
            `Checkout - ${formatPrice(cart.totalAmount)}`
          )}
        </Button>

        <Button
          variant="secondary"
          onClick={handleClearCart}
          disabled={isClearingCart || cart.items.length === 0}
          className="w-full"
        >
          {isClearingCart ? (
            <div className="flex items-center justify-center">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-600 mr-2"></div>
              Clearing...
            </div>
          ) : (
            'Clear Cart'
          )}
        </Button>

        <Button as="a" href="/products" variant="secondary" className="w-full">
          Continue Shopping
        </Button>
      </div>

      {/* Security Notice */}
      <div className="mt-6 pt-6 border-t">
        <div className="flex items-center text-sm text-gray-600">
          <svg
            className="h-4 w-4 text-green-500 mr-2"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
            />
          </svg>
          Secure checkout powered by SSL encryption
        </div>
      </div>

      {/* Clear Cart Confirmation Modal */}
      <ConfirmationModal
        isOpen={showClearConfirmation}
        onClose={handleClearCancel}
        onConfirm={handleClearConfirm}
        title="Clear Cart"
        message={`Are you sure you want to clear your cart? This will remove all ${formatQuantity(cart.totalItems)} item${cart.totalItems !== 1 ? 's' : ''} from your cart. This action cannot be undone.`}
        confirmText="Clear Cart"
        cancelText="Cancel"
        type="warning"
        isLoading={isClearingCart}
      />
    </div>
  );
};
