import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export type Product = {
  id: string;
  name: string;
  description: string | null;
  price: number;
  imageUrl: string | null;
  stock: number;
  category: string | null;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
};

export type CreateProductData = {
  name: string;
  description?: string;
  price: number;
  imageUrl?: string;
  stock: number;
  category?: string;
  isActive?: boolean;
};

export type UpdateProductData = Partial<CreateProductData>;

export async function getProducts(
  page: number = 1,
  limit: number = 10,
  category?: string,
  search?: string,
  activeOnly: boolean = true
): Promise<{ products: Product[]; total: number; totalPages: number }> {
  const skip = (page - 1) * limit;

  const where: any = {};

  if (activeOnly) {
    where.isActive = true;
  }

  if (category) {
    where.category = category;
  }

  if (search) {
    where.OR = [
      { name: { contains: search, mode: 'insensitive' } },
      { description: { contains: search, mode: 'insensitive' } },
    ];
  }

  const [products, total] = await Promise.all([
    prisma.product.findMany({
      where,
      skip,
      take: limit,
      orderBy: { createdAt: 'desc' },
    }),
    prisma.product.count({ where }),
  ]);

  return {
    products,
    total,
    totalPages: Math.ceil(total / limit),
  };
}

export async function getProductById(id: string): Promise<Product | null> {
  return prisma.product.findUnique({
    where: { id },
  });
}

export async function createProduct(data: CreateProductData): Promise<Product> {
  return prisma.product.create({
    data,
  });
}

export async function updateProduct(id: string, data: UpdateProductData): Promise<Product> {
  return prisma.product.update({
    where: { id },
    data,
  });
}

export async function deleteProduct(id: string): Promise<Product> {
  return prisma.product.update({
    where: { id },
    data: { isActive: false }, // Soft delete
  });
}

export async function getCategories(): Promise<string[]> {
  const categories = await prisma.product.findMany({
    where: { isActive: true },
    select: { category: true },
    distinct: ['category'],
  });

  return categories.map(c => c.category).filter(Boolean) as string[];
}

export async function updateStock(id: string, quantity: number): Promise<Product> {
  return prisma.product.update({
    where: { id },
    data: {
      stock: {
        decrement: quantity,
      },
    },
  });
}
