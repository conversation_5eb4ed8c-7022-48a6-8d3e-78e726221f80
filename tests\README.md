# Test Structure - Role-Based Organization

## 📂 Directory Structure

```
tests/
├── 📂 config/                   # Test configuration and setup
│   ├── auth.setup.js           # Authentication state generation
│   └── auth.setup.test.js      # Setup test runner
│
├── 📂 pages/                    # Page Object Models (POM)
│   ├── LoginPage.ts            # Login page interactions
│   ├── RegisterPage.ts         # Registration page interactions
│   ├── DashboardPage.ts        # Dashboard page interactions
│   ├── UsersPage.ts            # User management page
│   ├── ProductsPage.ts         # Products browsing page
│   └── CartPage.ts             # Shopping cart page
│
├── 📂 roles/                    # Role-based test organization
│   ├── 📂 unauthenticated/     # Tests for non-logged users
│   │   └── auth.spec.ts        # Authentication tests (6 tests)
│   │
│   ├── 📂 admin/               # Admin role tests
│   │   ├── access-control/     # Admin access control tests
│   │   │   └── permissions.spec.ts  # Admin permissions (6 tests)
│   │   └── user-management/    # Admin user management tests
│   │       └── users.spec.ts   # User CRUD operations (6 tests)
│   │
│   └── 📂 member/              # Member role tests
│       ├── access-control/     # Member access control tests
│       │   └── permissions.spec.ts  # Member permissions (5 tests)
│       └── shopping/           # Member shopping tests
│           └── cart.spec.ts    # Shopping cart functionality (7 tests)
│
└── 📂 .auth/                    # Generated authentication states
    ├── admin.json              # Admin authentication state
    └── member.json             # Member authentication state
```

## 🎯 Role-Based Test Organization

### **🔓 Unauthenticated Tests**

- **Location**: `tests/role/unauthenticated/`
- **Purpose**: Tests for users who are not logged in
- **Examples**: Login, registration, public pages
- **Storage State**: None (no authentication required)

### **👑 Admin Tests**

- **Location**: `tests/role/admin/`
- **Purpose**: Tests for admin users with full permissions
- **Examples**: User management, admin dashboards, system configuration
- **Storage State**: `tests/.auth/admin.json`

### **👤 Member Tests**

- **Location**: `tests/role/member/`
- **Purpose**: Tests for regular member users
- **Examples**: Shopping, cart management, profile updates
- **Storage State**: `tests/.auth/member.json`

## 🚀 Usage Examples

### **🔧 Running Tests by Role:**

```bash
# Run all unauthenticated tests
npm run test:e2e:unauthenticated

# Run all admin tests
npm run test:e2e:admin

# Run all member tests
npm run test:e2e:member

# Run all tests
npm run test:e2e
```

### **🔧 Running Specific Test Files:**

```bash
# Run specific admin tests
npx playwright test tests/role/admin/user-management/

# Run specific member tests
npx playwright test tests/role/member/shopping/

# Run authentication tests
npx playwright test tests/role/unauthenticated/
```

### **🔧 Setup and Development:**

```bash
# Generate authentication states
npm run test:e2e:setup

# Run tests with UI for debugging
npm run test:e2e:ui

# Run tests in headed mode
npx playwright test --headed
```

## 🔧 Configuration

### **📋 Playwright Projects:**

- **setup**: Generates authentication states
- **unauthenticated**: Tests without authentication
- **admin**: Tests with admin authentication
- **member**: Tests with member authentication

### **📋 Authentication States:**

- Admin and member authentication states are generated once and reused
- This eliminates the need for repeated login operations
- Tests run 4x faster with pre-authenticated states

## 🎯 Benefits

### **✅ Clear Role Separation:**

- Tests are organized by user roles and permissions
- Easy to understand what each test suite covers
- Clear ownership and responsibility

### **✅ Efficient Execution:**

- StorageState approach eliminates repeated logins
- Parallel execution without authentication conflicts
- Faster CI/CD pipeline execution

### **✅ Easy Maintenance:**

- Role-based organization matches application structure
- Simple to add new tests for specific roles
- Clear patterns for team members to follow

### **✅ Scalable Structure:**

- Easy to add new roles or permissions
- Tests can be run independently by role
- Supports both small and large test suites

## 📝 Adding New Tests

### **🔧 For Admin Tests:**

1. Create test file in `tests/role/admin/[feature]/`
2. Use admin storage state: `test.use({ storageState: 'tests/.auth/admin.json' })`
3. Import page objects from `../../pages/`

### **🔧 For Member Tests:**

1. Create test file in `tests/role/member/[feature]/`
2. Use member storage state: `test.use({ storageState: 'tests/.auth/member.json' })`
3. Import page objects from `../../pages/`

### **🔧 For Unauthenticated Tests:**

1. Create test file in `tests/role/unauthenticated/`
2. No storage state needed
3. Test login/registration flows

## 🎉 Best Practices

1. **Use appropriate storage states** for each role
2. **Keep tests focused** on specific role capabilities
3. **Use shared page objects** to reduce duplication
4. **Clean up test data** in beforeEach/afterEach hooks
5. **Follow consistent naming** conventions for test files
