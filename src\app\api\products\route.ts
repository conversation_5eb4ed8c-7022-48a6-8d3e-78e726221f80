import { NextRequest, NextResponse } from 'next/server';
import { getProducts, createProduct } from '@/features/products/services/productService';
import { getCurrentUser } from '@/features/auth/services/authService';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const category = searchParams.get('category') || undefined;
    const search = searchParams.get('search') || undefined;
    const activeOnly = searchParams.get('activeOnly') !== 'false';

    const result = await getProducts(page, limit, category, search, activeOnly);
    return NextResponse.json(result);
  } catch (error: any) {
    return NextResponse.json(
      { message: error.message || 'Failed to fetch products' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const currentUser = await getCurrentUser();

    if (!currentUser || currentUser.role !== 'admin') {
      return NextResponse.json({ message: 'Admin access required' }, { status: 403 });
    }

    const productData = await request.json();

    // Validate required fields
    if (!productData.name || !productData.price || productData.stock === undefined) {
      return NextResponse.json({ message: 'Name, price, and stock are required' }, { status: 400 });
    }

    // Validate price and stock are positive numbers
    if (productData.price <= 0 || productData.stock < 0) {
      return NextResponse.json(
        { message: 'Price must be positive and stock cannot be negative' },
        { status: 400 }
      );
    }

    const product = await createProduct(productData);

    return NextResponse.json(product, { status: 201 });
  } catch (error: any) {
    return NextResponse.json(
      { message: error.message || 'Failed to create product' },
      { status: 400 }
    );
  }
}
