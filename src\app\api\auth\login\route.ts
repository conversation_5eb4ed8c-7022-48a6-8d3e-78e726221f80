import { NextRequest, NextResponse } from 'next/server';
import { login } from '@/features/auth/services/authService';

export async function POST(request: NextRequest) {
  try {
    const { email, password } = await request.json();
    
    if (!email || !password) {
      return NextResponse.json(
        { message: 'Email and password are required' },
        { status: 400 }
      );
    }
    
    const user = await login(email, password);
    
    return NextResponse.json(user);
  } catch (error: any) {
    return NextResponse.json(
      { message: error.message || 'Lo<PERSON> failed' },
      { status: 401 }
    );
  }
}
