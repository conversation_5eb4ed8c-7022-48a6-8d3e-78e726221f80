#!/bin/sh
. "$(dirname "$0")/_/husky.sh"

echo "🔍 Running pre-commit checks..."

# 1. Run lint-staged for staged files (includes <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>)
echo "🧹 Running lint-staged..."
npx lint-staged
if [ $? -ne 0 ]; then
  echo "❌ Lint-staged failed. Please fix issues before committing."
  exit 1
fi
echo "✅ Lint-staged passed"

# 2. Run TypeScript type checking
echo "🔧 Running TypeScript type check..."
npm run type-check
if [ $? -ne 0 ]; then
  echo "❌ TypeScript type check failed. Please fix type errors before committing."
  exit 1
fi
echo "✅ TypeScript type check passed"

# 3. Run build to ensure everything compiles
echo "🏗️ Running build check..."
npm run build
if [ $? -ne 0 ]; then
  echo "❌ Build failed. Please fix build errors before committing."
  exit 1
fi
echo "✅ Build check passed"

echo "� Pre-commit checks passed! Proceeding with commit..."
echo "💡 Tip: Run 'npm run test:e2e' before pushing to ensure all tests pass."
