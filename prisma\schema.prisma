generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

enum Role {
  admin
  member
}

model User {
  id           String        @id @default(cuid())
  name         String
  email        String        @unique
  password     String
  role         Role          @default(member) // admin, member
  createdAt    DateTime      @default(now())
  cartItems    CartItem[]
  transactions Transaction[]
}

model Product {
  id          String     @id @default(cuid())
  name        String
  description String?
  price       Float
  imageUrl    String?
  stock       Int        @default(0)
  category    String?
  isActive    Boolean    @default(true)
  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @updatedAt
  cartItems   CartItem[]
  orderItems  OrderItem[]
}

model CartItem {
  id        String   @id @default(cuid())
  userId    String
  productId String
  quantity  Int      @default(1)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  product   Product  @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@unique([userId, productId])
}

model Transaction {
  id          String      @id @default(cuid())
  userId      String
  totalAmount Float
  status      String      @default("pending") // pending, completed, cancelled
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt
  user        User        @relation(fields: [userId], references: [id], onDelete: Cascade)
  orderItems  OrderItem[]
}

model OrderItem {
  id            String      @id @default(cuid())
  transactionId String
  productId     String
  quantity      Int
  price         Float       // Price at time of purchase
  createdAt     DateTime    @default(now())
  transaction   Transaction @relation(fields: [transactionId], references: [id], onDelete: Cascade)
  product       Product     @relation(fields: [productId], references: [id], onDelete: Cascade)
}
