'use client';

import React, { useState } from 'react';
import { Product } from '../services/productService';
import { useCart } from '@/features/cart/hooks/useCart';
import { useAuth } from '@/features/auth/hooks/useAuth';
import { Button } from '@/shared/components/Button';
import { toast } from 'react-hot-toast';
import { formatPrice, formatStock } from '@/shared/utils/formatNumber';

interface ProductCardProps {
  product: Product;
  onEdit?: (_product: Product) => void;
  onDelete?: (_product: Product) => void;
  showActions?: boolean;
}

export const ProductCard: React.FC<ProductCardProps> = ({
  product,
  onEdit,
  onDelete,
  showActions = false,
}) => {
  const { user } = useAuth();
  const { addToCart } = useCart();
  const [isAddingToCart, setIsAddingToCart] = useState(false);

  const canAddToCart = user && user.role === 'member';
  const isAdmin = user?.role === 'admin';

  const handleAddToCart = async () => {
    if (!canAddToCart) {
      toast.error('Only members can add items to cart');
      return;
    }

    // Prevent double-clicks
    if (isAddingToCart) {
      return;
    }

    try {
      setIsAddingToCart(true);
      await addToCart(product.id, 1);
      toast.success(`${product.name} added to cart!`);
    } catch (error: any) {
      toast.error(error.message || 'Failed to add to cart');
    } finally {
      setIsAddingToCart(false);
    }
  };

  return (
    <div
      className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300 flex flex-col h-full"
      data-testid="product-card"
    >
      {/* Product Image */}
      <div className="aspect-w-1 aspect-h-1 w-full overflow-hidden bg-gray-200">
        {product.imageUrl ? (
          <img
            src={product.imageUrl}
            alt={product.name}
            className="h-48 w-full object-cover object-center group-hover:opacity-75"
          />
        ) : (
          <div className="h-48 w-full bg-gray-200 flex items-center justify-center">
            <svg
              className="h-12 w-12 text-gray-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
              />
            </svg>
          </div>
        )}
      </div>

      {/* Product Info */}
      <div className="p-4 flex flex-col h-full">
        <div className="flex flex-col items-center mb-2">
          <h3 className="text-lg font-semibold text-gray-900 line-clamp-2 text-center mb-2">
            {product.name}
          </h3>
          {product.category && (
            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
              {product.category}
            </span>
          )}
        </div>

        {product.description && (
          <p className="text-sm text-gray-600 mb-6 line-clamp-2">{product.description}</p>
        )}

        {/* Action Buttons - Push to bottom with 24px padding */}
        <div className="mt-auto pb-6">
          {/* Price and Stock - directly above Add to Cart button with 16px margin */}
          <div className="flex justify-between items-center mb-4">
            <span className="text-2xl font-bold text-gray-900">{formatPrice(product.price)}</span>
            <span
              className={`text-sm font-medium ${
                product.stock > 0 ? 'text-green-600' : 'text-red-600'
              }`}
            >
              {product.stock > 0 ? formatStock(product.stock) : 'Out of stock'}
            </span>
          </div>

          <div className="space-y-2">
            {canAddToCart && (
              <Button
                onClick={handleAddToCart}
                disabled={isAddingToCart || product.stock === 0}
                className="w-full"
                variant={product.stock === 0 ? 'secondary' : 'primary'}
              >
                {(() => {
                  if (product.stock === 0) {
                    return 'Out of Stock';
                  }
                  return 'Add to Cart';
                })()}
              </Button>
            )}

            {showActions && isAdmin && (
              <div className="flex space-x-2">
                <Button onClick={() => onEdit?.(product)} variant="secondary" className="flex-1">
                  Edit
                </Button>
                <Button onClick={() => onDelete?.(product)} variant="danger" className="flex-1">
                  Delete
                </Button>
              </div>
            )}
          </div>
        </div>
      </div>

      {!product.isActive && (
        <div className="absolute inset-0 bg-gray-900 bg-opacity-50 flex items-center justify-center">
          <span className="bg-red-600 text-white px-3 py-1 rounded-full text-sm font-medium">
            Inactive
          </span>
        </div>
      )}
    </div>
  );
};
