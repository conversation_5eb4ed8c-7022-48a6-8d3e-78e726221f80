import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export type Transaction = {
  id: string;
  userId: string;
  totalAmount: number;
  status: string;
  createdAt: Date;
  updatedAt: Date;
  user: {
    id: string;
    name: string;
    email: string;
  };
  orderItems: {
    id: string;
    quantity: number;
    price: number;
    product: {
      id: string;
      name: string;
      imageUrl: string | null;
    };
  }[];
};

export type CreateTransactionData = {
  userId: string;
  cartItems: {
    productId: string;
    quantity: number;
    price: number;
  }[];
};

export async function createTransaction(data: CreateTransactionData): Promise<Transaction> {
  const totalAmount = data.cartItems.reduce((sum, item) => sum + item.quantity * item.price, 0);

  return prisma.$transaction(async tx => {
    // Create transaction
    const transaction = await tx.transaction.create({
      data: {
        userId: data.userId,
        totalAmount,
        status: 'pending',
      },
    });

    // Create order items
    const _orderItems = await Promise.all(
      data.cartItems.map(item =>
        tx.orderItem.create({
          data: {
            transactionId: transaction.id,
            productId: item.productId,
            quantity: item.quantity,
            price: item.price,
          },
        })
      )
    );

    // Update product stock
    await Promise.all(
      data.cartItems.map(item =>
        tx.product.update({
          where: { id: item.productId },
          data: {
            stock: {
              decrement: item.quantity,
            },
          },
        })
      )
    );

    // Clear user's cart
    await tx.cartItem.deleteMany({
      where: { userId: data.userId },
    });

    // Return transaction with full details
    return tx.transaction.findUnique({
      where: { id: transaction.id },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        orderItems: {
          include: {
            product: {
              select: {
                id: true,
                name: true,
                imageUrl: true,
              },
            },
          },
        },
      },
    }) as Promise<Transaction>;
  });
}

export async function getTransactions(
  page: number = 1,
  limit: number = 10,
  userId?: string
): Promise<{ transactions: Transaction[]; total: number; totalPages: number }> {
  const skip = (page - 1) * limit;

  const where: any = {};
  if (userId) {
    where.userId = userId;
  }

  const [transactions, total] = await Promise.all([
    prisma.transaction.findMany({
      where,
      skip,
      take: limit,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        orderItems: {
          include: {
            product: {
              select: {
                id: true,
                name: true,
                imageUrl: true,
              },
            },
          },
        },
      },
      orderBy: { createdAt: 'desc' },
    }),
    prisma.transaction.count({ where }),
  ]);

  return {
    transactions,
    total,
    totalPages: Math.ceil(total / limit),
  };
}

export async function getTransactionById(id: string): Promise<Transaction | null> {
  return prisma.transaction.findUnique({
    where: { id },
    include: {
      user: {
        select: {
          id: true,
          name: true,
          email: true,
        },
      },
      orderItems: {
        include: {
          product: {
            select: {
              id: true,
              name: true,
              imageUrl: true,
            },
          },
        },
      },
    },
  });
}

export async function updateTransactionStatus(id: string, status: string): Promise<Transaction> {
  return prisma.transaction.update({
    where: { id },
    data: { status },
    include: {
      user: {
        select: {
          id: true,
          name: true,
          email: true,
        },
      },
      orderItems: {
        include: {
          product: {
            select: {
              id: true,
              name: true,
              imageUrl: true,
            },
          },
        },
      },
    },
  });
}

export async function getUserTransactions(userId: string): Promise<Transaction[]> {
  return prisma.transaction.findMany({
    where: { userId },
    include: {
      user: {
        select: {
          id: true,
          name: true,
          email: true,
        },
      },
      orderItems: {
        include: {
          product: {
            select: {
              id: true,
              name: true,
              imageUrl: true,
            },
          },
        },
      },
    },
    orderBy: { createdAt: 'desc' },
  });
}
