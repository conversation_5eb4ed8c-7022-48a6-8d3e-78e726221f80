import { NextRequest, NextResponse } from 'next/server';
import {
  getProductById,
  updateProduct,
  deleteProduct,
} from '@/features/products/services/productService';
import { getCurrentUser } from '@/features/auth/services/authService';

export async function GET(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    const { id } = await params;
    const product = await getProductById(id);

    if (!product) {
      return NextResponse.json({ message: 'Product not found' }, { status: 404 });
    }

    return NextResponse.json(product);
  } catch (error: any) {
    return NextResponse.json(
      { message: error.message || 'Failed to fetch product' },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    const currentUser = await getCurrentUser();

    if (!currentUser || currentUser.role !== 'admin') {
      return NextResponse.json({ message: 'Admin access required' }, { status: 403 });
    }

    const productData = await request.json();
    const { id } = await params;

    // Validate price and stock if provided
    if (productData.price !== undefined && productData.price <= 0) {
      return NextResponse.json({ message: 'Price must be positive' }, { status: 400 });
    }

    if (productData.stock !== undefined && productData.stock < 0) {
      return NextResponse.json({ message: 'Stock cannot be negative' }, { status: 400 });
    }

    const product = await updateProduct(id, productData);

    return NextResponse.json(product);
  } catch (error: any) {
    return NextResponse.json(
      { message: error.message || 'Failed to update product' },
      { status: 400 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const currentUser = await getCurrentUser();

    if (!currentUser || currentUser.role !== 'admin') {
      return NextResponse.json({ message: 'Admin access required' }, { status: 403 });
    }

    const { id } = await params;
    const product = await deleteProduct(id);

    return NextResponse.json(product);
  } catch (error: any) {
    return NextResponse.json(
      { message: error.message || 'Failed to delete product' },
      { status: 400 }
    );
  }
}
