import { NextResponse } from 'next/server';
import { getCategories } from '@/features/products/services/productService';

export async function GET() {
  try {
    const categories = await getCategories();
    return NextResponse.json(categories);
  } catch (error: any) {
    return NextResponse.json(
      { message: error.message || 'Failed to fetch categories' },
      { status: 500 }
    );
  }
}
