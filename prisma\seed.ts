import { PrismaClient } from '@prisma/client';
import { hash } from 'bcryptjs';

const prisma = new PrismaClient();

async function main() {
  // Create admin user
  await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      name: 'Admin User',
      email: '<EMAIL>',
      password: await hash('password123', 10),
      role: 'admin',
    },
  });

  // Create member user
  await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      name: 'Member User',
      email: '<EMAIL>',
      password: await hash('password123', 10),
      role: 'member',
    },
  });

  // Create sample products
  const products = [
    {
      name: 'Wireless Bluetooth Headphones',
      description:
        'High-quality wireless headphones with noise cancellation and 30-hour battery life.',
      price: 199.99,
      imageUrl: 'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=500&h=500&fit=crop',
      stock: 50,
      category: 'Electronics',
    },
    {
      name: 'Smart Fitness Watch',
      description:
        'Advanced fitness tracker with heart rate monitoring, GPS, and waterproof design.',
      price: 299.99,
      imageUrl: 'https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=500&h=500&fit=crop',
      stock: 30,
      category: 'Electronics',
    },
    {
      name: 'Organic Coffee Beans',
      description:
        'Premium organic coffee beans sourced from sustainable farms. Rich and aromatic.',
      price: 24.99,
      imageUrl: 'https://images.unsplash.com/photo-1559056199-641a0ac8b55e?w=500&h=500&fit=crop',
      stock: 100,
      category: 'Food & Beverage',
    },
    {
      name: 'Ergonomic Office Chair',
      description: 'Comfortable ergonomic office chair with lumbar support and adjustable height.',
      price: 449.99,
      imageUrl: 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=500&h=500&fit=crop',
      stock: 15,
      category: 'Furniture',
    },
    {
      name: 'Wireless Charging Pad',
      description: 'Fast wireless charging pad compatible with all Qi-enabled devices.',
      price: 39.99,
      imageUrl: 'https://images.unsplash.com/photo-1609592806596-b43bada2d7b5?w=500&h=500&fit=crop',
      stock: 75,
      category: 'Electronics',
    },
    {
      name: 'Yoga Mat Premium',
      description: 'Non-slip premium yoga mat with extra cushioning for comfort during workouts.',
      price: 79.99,
      imageUrl: 'https://images.unsplash.com/photo-1544367567-0f2fcb009e0b?w=500&h=500&fit=crop',
      stock: 40,
      category: 'Sports & Fitness',
    },
    {
      name: 'Stainless Steel Water Bottle',
      description: 'Insulated stainless steel water bottle that keeps drinks cold for 24 hours.',
      price: 34.99,
      imageUrl: 'https://images.unsplash.com/photo-1602143407151-7111542de6e8?w=500&h=500&fit=crop',
      stock: 60,
      category: 'Sports & Fitness',
    },
    {
      name: 'LED Desk Lamp',
      description:
        'Adjustable LED desk lamp with multiple brightness levels and USB charging port.',
      price: 89.99,
      imageUrl: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=500&h=500&fit=crop',
      stock: 25,
      category: 'Home & Office',
    },
  ];

  for (const product of products) {
    // Check if product already exists by name
    const existingProduct = await prisma.product.findFirst({
      where: { name: product.name },
    });

    if (!existingProduct) {
      await prisma.product.create({
        data: product,
      });
    }
  }

  console.log('Database seeded successfully with users and products');
}

main()
  .catch(e => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
