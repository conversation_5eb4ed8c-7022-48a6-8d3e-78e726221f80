'use client';

import React from 'react';
import { useCart } from '@/features/cart/hooks/useCart';
import { CartItem } from '@/features/cart/components/CartItem';
import { CartSummary } from '@/features/cart/components/CartSummary';
import { useAuth } from '@/features/auth/hooks/useAuth';
import { formatPrice, formatItemCount } from '@/shared/utils/formatNumber';

export default function CartPage() {
  const { user } = useAuth();
  const { cart, loading, error, updateQuantity, removeItem, clearCart, checkout } = useCart();

  // Check if user can access cart
  if (user && user.role !== 'member') {
    return (
      <div className="text-center py-12">
        <div className="text-orange-600 mb-4">
          <svg className="mx-auto h-12 w-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
            />
          </svg>
        </div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">Access Restricted</h3>
        <p className="text-gray-600 mb-4">
          {user.role === 'admin'
            ? 'Admin users should use the transaction management system to view orders.'
            : 'You need to be a member to access the shopping cart.'}
        </p>
        {user.role === 'admin' && (
          <a
            href="/admin/transactions"
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          >
            Go to Transaction Management
          </a>
        )}
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <div className="text-red-600 mb-4">
          <svg className="mx-auto h-12 w-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
            />
          </svg>
        </div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">Error loading cart</h3>
        <p className="text-gray-600">{error}</p>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="flex justify-center py-12">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="bg-white shadow-sm rounded-lg p-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Shopping Cart</h1>
            <p className="text-gray-600 mt-1">
              {cart.totalItems > 0
                ? `${formatItemCount(cart.totalItems)} in your cart`
                : 'Your cart is empty'}
            </p>
          </div>
          {cart.items.length > 0 && (
            <div className="text-right">
              <div className="text-2xl font-bold text-gray-900">
                {formatPrice(cart.totalAmount)}
              </div>
              <div className="text-sm text-gray-600">Total</div>
            </div>
          )}
        </div>
      </div>

      {cart.items.length > 0 ? (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Cart Items */}
          <div className="lg:col-span-2">
            <div className="bg-white shadow-sm rounded-lg p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-6">Cart Items</h2>
              <div className="space-y-0">
                {cart.items.map(item => (
                  <CartItem
                    key={item.id}
                    item={item}
                    onUpdateQuantity={updateQuantity}
                    onRemove={removeItem}
                  />
                ))}
              </div>
            </div>
          </div>

          {/* Cart Summary */}
          <div className="lg:col-span-1">
            <CartSummary cart={cart} onCheckout={checkout} onClearCart={clearCart} />
          </div>
        </div>
      ) : (
        <CartSummary cart={cart} onCheckout={checkout} onClearCart={clearCart} />
      )}
    </div>
  );
}
