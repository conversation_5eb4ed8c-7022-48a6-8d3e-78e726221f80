import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export type CartItem = {
  id: string;
  userId: string;
  productId: string;
  quantity: number;
  createdAt: Date;
  updatedAt: Date;
  product: {
    id: string;
    name: string;
    price: number;
    imageUrl: string | null;
    stock: number;
    isActive: boolean;
  };
};

export type CartSummary = {
  items: CartItem[];
  totalItems: number;
  totalAmount: number;
};

export async function getCartItems(userId: string): Promise<CartSummary> {
  const items = await prisma.cartItem.findMany({
    where: { userId },
    include: {
      product: {
        select: {
          id: true,
          name: true,
          price: true,
          imageUrl: true,
          stock: true,
          isActive: true,
        },
      },
    },
    orderBy: { createdAt: 'desc' },
  });

  const totalItems = items.reduce((sum, item) => sum + item.quantity, 0);
  const totalAmount = items.reduce((sum, item) => sum + item.quantity * item.product.price, 0);

  return {
    items,
    totalItems,
    totalAmount,
  };
}

export async function addToCart(
  userId: string,
  productId: string,
  quantity: number = 1
): Promise<CartItem> {
  // Check if product exists and is active
  const product = await prisma.product.findUnique({
    where: { id: productId },
  });

  if (!product || !product.isActive) {
    throw new Error('Product not found or inactive');
  }

  if (product.stock < quantity) {
    throw new Error('Insufficient stock');
  }

  // Check if item already exists in cart
  const existingItem = await prisma.cartItem.findUnique({
    where: {
      userId_productId: {
        userId,
        productId,
      },
    },
  });

  if (existingItem) {
    // Update quantity
    const newQuantity = existingItem.quantity + quantity;

    if (product.stock < newQuantity) {
      throw new Error('Insufficient stock');
    }

    return prisma.cartItem.update({
      where: { id: existingItem.id },
      data: { quantity: newQuantity },
      include: {
        product: {
          select: {
            id: true,
            name: true,
            price: true,
            imageUrl: true,
            stock: true,
            isActive: true,
          },
        },
      },
    });
  } else {
    // Create new cart item
    return prisma.cartItem.create({
      data: {
        userId,
        productId,
        quantity,
      },
      include: {
        product: {
          select: {
            id: true,
            name: true,
            price: true,
            imageUrl: true,
            stock: true,
            isActive: true,
          },
        },
      },
    });
  }
}

export async function updateCartItemQuantity(
  userId: string,
  cartItemId: string,
  quantity: number
): Promise<CartItem> {
  if (quantity <= 0) {
    throw new Error('Quantity must be greater than 0');
  }

  const cartItem = await prisma.cartItem.findFirst({
    where: { id: cartItemId, userId },
    include: { product: true },
  });

  if (!cartItem) {
    throw new Error('Cart item not found');
  }

  if (cartItem.product.stock < quantity) {
    throw new Error('Insufficient stock');
  }

  return prisma.cartItem.update({
    where: { id: cartItemId },
    data: { quantity },
    include: {
      product: {
        select: {
          id: true,
          name: true,
          price: true,
          imageUrl: true,
          stock: true,
          isActive: true,
        },
      },
    },
  });
}

export async function removeFromCart(userId: string, cartItemId: string): Promise<void> {
  const cartItem = await prisma.cartItem.findFirst({
    where: { id: cartItemId, userId },
  });

  if (!cartItem) {
    throw new Error('Cart item not found');
  }

  await prisma.cartItem.delete({
    where: { id: cartItemId },
  });
}

export async function clearCart(userId: string): Promise<void> {
  await prisma.cartItem.deleteMany({
    where: { userId },
  });
}

export async function getCartItemCount(userId: string): Promise<number> {
  const result = await prisma.cartItem.aggregate({
    where: { userId },
    _sum: { quantity: true },
  });

  return result._sum.quantity || 0;
}
