const { PrismaClient } = require('@prisma/client');

async function testConnection() {
  const prisma = new PrismaClient();
  
  console.log('🔍 Testing PostgreSQL connection...\n');
  
  try {
    // Test basic connection
    await prisma.$connect();
    console.log('✅ Database connection successful!');
    
    // Test query execution
    const result = await prisma.$queryRaw`SELECT version()`;
    console.log('✅ Query execution successful!');
    console.log(`📊 PostgreSQL Version: ${result[0].version}\n`);
    
    // Check if User table exists
    try {
      const userCount = await prisma.user.count();
      console.log(`✅ User table exists with ${userCount} records`);
      
      if (userCount > 0) {
        const users = await prisma.user.findMany({
          select: { id: true, name: true, email: true, role: true }
        });
        console.log('👥 Existing users:');
        users.forEach(user => {
          console.log(`   - ${user.name} (${user.email}) - ${user.role}`);
        });
      }
    } catch (error) {
      console.log('⚠️  User table not found - run "npm run db:push" to create schema');
    }
    
  } catch (error) {
    console.log('❌ Database connection failed!');
    console.log('Error:', error.message);
    
    if (error.code === 'ECONNREFUSED') {
      console.log('\n💡 Possible solutions:');
      console.log('   1. Make sure PostgreSQL is running');
      console.log('   2. Check if Docker container is started: docker compose up postgres -d');
      console.log('   3. Verify connection string in .env.local');
      console.log('   4. Check if database "fullstack_app" exists');
    }
    
    if (error.code === 'ENOTFOUND') {
      console.log('\n💡 Possible solutions:');
      console.log('   1. Check hostname in DATABASE_URL');
      console.log('   2. Verify internet connection for cloud databases');
      console.log('   3. Make sure the database server is accessible');
    }
    
    if (error.message.includes('password authentication failed')) {
      console.log('\n💡 Possible solutions:');
      console.log('   1. Check username and password in DATABASE_URL');
      console.log('   2. Verify database credentials');
    }
    
    if (error.message.includes('database') && error.message.includes('does not exist')) {
      console.log('\n💡 Possible solutions:');
      console.log('   1. Create the database manually');
      console.log('   2. Check database name in CONNECTION_URL');
    }
  } finally {
    await prisma.$disconnect();
  }
}

testConnection();
