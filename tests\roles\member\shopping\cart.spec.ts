import { test, expect } from '@playwright/test';
import { ProductsPage } from '../../../pages/ProductsPage';
import { CartPage } from '../../../pages/CartPage';

// Use member storage state for all tests in this file
test.use({ storageState: 'tests/.auth/member.json' });

test.describe('Member - Shopping Cart', () => {
  let productsPage: ProductsPage;
  let cartPage: CartPage;

  test.beforeEach(async ({ page }) => {
    productsPage = new ProductsPage(page);
    cartPage = new CartPage(page);

    // Clear cart using test API to ensure clean state
    await page.evaluate(async () => {
      try {
        await fetch('/api/cart/clear-test', { method: 'POST' });
      } catch (error) {
        // Ignore cart clear errors
      }
    });

    // Wait a bit for cart to be cleared and UI to update
    await page.waitForTimeout(1000);
  });

  test('should add items to cart from products page', async ({ page }) => {
    // Navigate to products page
    await productsPage.goto();
    await expect(page.locator('h1')).toContainText('Products');

    // Wait for products to load
    await productsPage.waitForProducts();
    const productCount = await productsPage.getProductsCount();
    expect(productCount).toBeGreaterThan(0);

    // Get initial cart count
    const initialCount = await productsPage.getCartCount();

    // Find and add an available product to cart
    await productsPage.addAvailableProductToCart();

    // Verify cart count increased by 1
    const expectedCount = initialCount + 1;
    await expect(productsPage.cartCount).toContainText(expectedCount.toString());

    // Navigate to cart and verify item was added
    await cartPage.goto();
    await expect(page.locator('h1')).toContainText('Shopping Cart');

    // Verify cart has items
    const cartItemCount = await cartPage.cartItems.count();
    expect(cartItemCount).toBeGreaterThan(0);
  });

  test('should update cart count when adding multiple items', async ({ page }) => {
    // Navigate to products page
    await productsPage.goto();
    await expect(page.locator('h1')).toContainText('Products');

    // Wait for products to load
    await productsPage.waitForProducts();
    const productCount = await productsPage.getProductsCount();
    expect(productCount).toBeGreaterThan(0);

    // Get initial cart count (should be 0 after beforeEach clear, but be flexible)
    const initialCount = await productsPage.getCartCount();

    // Add multiple available products with proper waits
    let addedCount = 0;
    try {
      await productsPage.addAvailableProductToCart();
      addedCount++;
      // Wait for cart count to update after first addition
      await expect(productsPage.cartCount).toContainText((initialCount + 1).toString(), { timeout: 10000 });

      await productsPage.addAvailableProductToCart();
      addedCount++;
      // Wait for cart count to update after second addition
      await expect(productsPage.cartCount).toContainText((initialCount + 2).toString(), { timeout: 10000 });

      await productsPage.addAvailableProductToCart();
      addedCount++;
      // Wait for cart count to update after third addition
      await expect(productsPage.cartCount).toContainText((initialCount + 3).toString(), { timeout: 10000 });
    } catch (error) {
      // Continue with whatever we managed to add
    }

    // Verify cart count increased
    expect(addedCount).toBeGreaterThan(0);
    const expectedCount = initialCount + addedCount;
    await expect(productsPage.cartCount).toContainText(expectedCount.toString(), { timeout: 10000 });
  });

  test('should update item quantity in cart', async ({ page }) => {
    // Navigate to products page and add item
    await productsPage.goto();
    await expect(page.locator('h1')).toContainText('Products');
    await productsPage.waitForProducts();
    await productsPage.addAvailableProductToCart();

    // Navigate to cart page
    await cartPage.goto();
    await expect(page.locator('h1')).toContainText('Shopping Cart');

    // Wait for cart items to be visible
    await expect(cartPage.cartItems.first()).toBeVisible();
    const cartItemCount = await cartPage.cartItems.count();
    expect(cartItemCount).toBeGreaterThan(0);

    // Get initial quantity
    const initialQuantity = await cartPage.getQuantity(0);
    const initialQtyText = await initialQuantity.textContent();
    const initialQtyNum = parseInt(initialQtyText || '1');

    // Get initial total for comparison
    const initialTotal = await cartPage.getTotalAmount();

    // Increase quantity
    await cartPage.increaseQuantity(0);
    await page.waitForTimeout(1000); // Wait for update

    // Verify quantity increased by 1
    const updatedQuantity = await cartPage.getQuantity(0);
    await expect(updatedQuantity).toContainText((initialQtyNum + 1).toString());

    // Verify total amount increased
    const newTotal = await cartPage.getTotalAmount();
    expect(parseFloat(newTotal)).toBeGreaterThan(parseFloat(initialTotal));
  });

  test('should remove item from cart', async ({ page }) => {
    // Navigate to products page and add item
    await productsPage.goto();
    await expect(page.locator('h1')).toContainText('Products');
    await productsPage.waitForProducts();
    await productsPage.addAvailableProductToCart();

    // Navigate to cart page
    await cartPage.goto();
    await expect(page.locator('h1')).toContainText('Shopping Cart');
    await expect(cartPage.cartItems.first()).toBeVisible();

    // Get initial item count
    const initialItemCount = await cartPage.cartItems.count();
    expect(initialItemCount).toBeGreaterThan(0);

    // Verify remove button is visible before clicking
    const firstCartItem = cartPage.cartItems.first();
    const removeButton = firstCartItem
      .locator('button[data-testid="remove-item"]')
      .or(firstCartItem.locator('button:has-text("Remove")'))
      .or(firstCartItem.locator('button:has([data-testid="trash-icon"])'))
      .or(firstCartItem.locator('button.text-red-600'))
      .or(firstCartItem.locator('button').last());

    await expect(removeButton).toBeVisible();

    // Remove first item
    await cartPage.removeItem(0);
    await page.waitForTimeout(1000); // Wait for removal

    // Check if cart is now empty or has fewer items
    const newItemCount = await cartPage.cartItems.count();

    if (newItemCount === 0) {
      // Cart should show empty message
      await expect(cartPage.emptyCartMessage).toBeVisible();
    } else {
      // Item count should have decreased
      expect(newItemCount).toBe(initialItemCount - 1);
    }
  });

  test('should clear entire cart', async ({ page }) => {
    // Navigate to products page and add multiple items
    await productsPage.goto();
    await expect(page.locator('h1')).toContainText('Products');

    // Add multiple items
    await productsPage.addAvailableProductToCart();
    await productsPage.addAvailableProductToCart();

    // Navigate to cart page
    await cartPage.goto();
    await expect(page.locator('h1')).toContainText('Shopping Cart');
    const itemCount = await cartPage.cartItems.count();
    expect(itemCount).toBeGreaterThan(0);

    // Clear cart
    await cartPage.clearCart();

    // Wait for cart to be cleared (check that cart items are gone)
    await expect(cartPage.cartItems).toHaveCount(0);

    // Cart should be empty
    await expect(cartPage.emptyCartMessage).toBeVisible();
  });

  test('should show empty cart message when no items', async ({ page }) => {
    await cartPage.goto();
    await cartPage.clearCart();

    // Should see empty cart message
    await expect(cartPage.emptyCartMessage).toBeVisible();
    await expect(page.locator('h3:has-text("Your cart is empty")')).toBeVisible();
    await expect(page.locator('text=Add some products to get started')).toBeVisible();
  });

  test('should calculate correct totals', async ({ page }) => {
    // Navigate to products page
    await productsPage.goto();
    await expect(page.locator('h1')).toContainText('Products');
    await productsPage.waitForProducts();

    // Find an available product and get its price
    const availableProductIndex = await productsPage.findAvailableProductIndex();
    const productPrice = await productsPage.getProductPrice(availableProductIndex);

    // Add that specific product to cart
    await productsPage.addToCart(availableProductIndex);

    // Navigate to cart page
    await cartPage.goto();
    await expect(page.locator('h1')).toContainText('Shopping Cart');
    await expect(cartPage.cartItems.first()).toBeVisible();

    // Check if total matches product price
    const cartTotal = await cartPage.getTotalAmount();
    expect(parseFloat(cartTotal)).toBe(parseFloat(productPrice));
  });
});
