import React from 'react';
import Link from 'next/link';

type BaseButtonProps = {
  children: React.ReactNode;
  variant?: 'primary' | 'secondary' | 'danger' | 'warning';
  disabled?: boolean;
  className?: string;
};

type ButtonAsButton = BaseButtonProps & {
  as?: 'button';
  type?: 'button' | 'submit' | 'reset';
  onClick?: () => void;
  href?: never;
};

type ButtonAsLink = BaseButtonProps & {
  as: 'a';
  href: string;
  type?: never;
  onClick?: never;
};

type ButtonProps = ButtonAsButton | ButtonAsLink;

export const Button: React.FC<ButtonProps> = ({
  children,
  as = 'button',
  variant = 'primary',
  disabled = false,
  className = '',
  ...props
}) => {
  const baseStyles =
    'inline-flex items-center justify-center px-4 py-2 rounded font-medium focus:outline-none transition-colors';

  const variantStyles = {
    primary: 'bg-blue-600 hover:bg-blue-700 text-white disabled:bg-blue-300',
    secondary: 'bg-gray-200 hover:bg-gray-300 text-gray-800 disabled:bg-gray-100',
    danger: 'bg-red-600 hover:bg-red-700 text-white disabled:bg-red-300',
    warning: 'bg-yellow-600 hover:bg-yellow-700 text-white disabled:bg-yellow-300',
  };

  const combinedClassName = `${baseStyles} ${variantStyles[variant]} ${disabled ? 'cursor-not-allowed' : ''} ${className}`;

  if (as === 'a') {
    const { href } = props as ButtonAsLink;
    return (
      <Link href={href} className={combinedClassName}>
        {children}
      </Link>
    );
  }

  const { type = 'button', onClick } = props as ButtonAsButton;
  return (
    <button type={type} onClick={onClick} disabled={disabled} className={combinedClassName}>
      {children}
    </button>
  );
};
