version: '3.8'

services:
  postgres:
    image: postgres:15
    container_name: fullstack-postgres
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
      POSTGRES_DB: fullstack_app
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

  app:
    build: .
    container_name: fullstack-app
    ports:
      - "3000:3000"
    environment:
      DATABASE_URL: "********************************************/fullstack_app?schema=public"
      JWT_SECRET: "your-super-secret-jwt-key-change-this-in-production"
      NEXTAUTH_URL: "http://localhost:3000"
    depends_on:
      postgres:
        condition: service_healthy
    volumes:
      - .:/app
      - /app/node_modules
      - /app/.next

volumes:
  postgres_data:
