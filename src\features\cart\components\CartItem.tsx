'use client';

import React, { useState, memo } from 'react';
import { CartItem as CartItemType } from '../services/cartService';
import { Button } from '@/shared/components/Button';
import { toast } from 'react-hot-toast';
import { formatPrice, formatQuantity, formatStock } from '@/shared/utils/formatNumber';

interface CartItemProps {
  item: CartItemType;
  onUpdateQuantity: (_itemId: string, _quantity: number) => Promise<void>;
  onRemove: (_itemId: string) => Promise<void>;
}

const CartItemComponent: React.FC<CartItemProps> = ({ item, onUpdateQuantity, onRemove }) => {
  const [isUpdating, setIsUpdating] = useState(false);
  const [isRemoving, setIsRemoving] = useState(false);

  const handleQuantityChange = async (newQuantity: number) => {
    if (newQuantity < 1) return;

    try {
      setIsUpdating(true);
      await onUpdateQuantity(item.id, newQuantity);
    } catch (error: any) {
      toast.error(error.message || 'Failed to update quantity');
    } finally {
      setIsUpdating(false);
    }
  };

  const handleRemove = async () => {
    try {
      setIsRemoving(true);
      await onRemove(item.id);
      toast.success('Item removed from cart');
    } catch (error: any) {
      toast.error(error.message || 'Failed to remove item');
    } finally {
      setIsRemoving(false);
    }
  };

  const subtotal = item.quantity * item.product.price;

  return (
    <div
      className="flex items-center space-x-4 py-6 border-b border-gray-200"
      data-testid="cart-item"
    >
      {/* Product Image */}
      <div className="flex-shrink-0 w-20 h-20">
        {item.product.imageUrl ? (
          <img
            src={item.product.imageUrl}
            alt={item.product.name}
            className="w-full h-full object-cover rounded-md"
          />
        ) : (
          <div className="w-full h-full bg-gray-200 rounded-md flex items-center justify-center">
            <svg
              className="h-8 w-8 text-gray-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
              />
            </svg>
          </div>
        )}
      </div>

      {/* Product Details */}
      <div className="flex-1 min-w-0">
        <h3 className="text-lg font-medium text-gray-900 truncate">{item.product.name}</h3>
        <p className="text-sm text-gray-600">{formatPrice(item.product.price)} each</p>
        {!item.product.isActive && (
          <p className="text-sm text-red-600 font-medium">⚠️ This product is no longer available</p>
        )}
        {item.product.stock < item.quantity && (
          <p className="text-sm text-orange-600 font-medium">
            ⚠️ Only {formatStock(item.product.stock)}
          </p>
        )}
      </div>

      {/* Quantity Controls */}
      <div className="flex items-center space-x-2">
        <Button
          variant="secondary"
          onClick={() => handleQuantityChange(item.quantity - 1)}
          disabled={isUpdating || item.quantity <= 1}
          className="w-8 h-8 p-0 text-lg flex items-center justify-center"
        >
          -
        </Button>

        <span className="w-12 text-center font-medium" data-testid="quantity">
          {isUpdating ? (
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mx-auto"></div>
          ) : (
            formatQuantity(item.quantity)
          )}
        </span>

        <Button
          variant="secondary"
          onClick={() => handleQuantityChange(item.quantity + 1)}
          disabled={isUpdating || item.quantity >= item.product.stock}
          className="w-8 h-8 p-0 text-lg flex items-center justify-center"
        >
          +
        </Button>
      </div>

      {/* Subtotal */}
      <div className="text-right">
        <p className="text-lg font-semibold text-gray-900">{formatPrice(subtotal)}</p>
      </div>

      {/* Remove Button */}
      <div>
        <button
          onClick={handleRemove}
          disabled={isRemoving}
          className="w-8 h-8 p-0 bg-red-600 hover:bg-red-700 text-white disabled:bg-red-300 rounded flex items-center justify-center transition-colors focus:outline-none focus:ring-2 focus:ring-red-500"
          data-testid="remove-item"
        >
          {isRemoving ? (
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
          ) : (
            <span className="text-white font-bold text-sm select-none" data-testid="trash-icon">
              🗑️
            </span>
          )}
        </button>
      </div>
    </div>
  );
};

// Memoize the component to prevent unnecessary re-renders
export const CartItem = memo(CartItemComponent, (prevProps, nextProps) => {
  // Only re-render if the item data actually changed
  return (
    prevProps.item.id === nextProps.item.id &&
    prevProps.item.quantity === nextProps.item.quantity &&
    prevProps.item.product.price === nextProps.item.product.price &&
    prevProps.item.product.stock === nextProps.item.product.stock &&
    prevProps.item.product.isActive === nextProps.item.product.isActive
  );
});
