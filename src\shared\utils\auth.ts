import { AuthUser } from '@/features/auth/services/authService';

export function isAdmin(user: AuthUser | null): boolean {
  return user?.role === 'admin';
}

export function isAuthenticated(user: AuthUser | null): boolean {
  return !!user;
}

export function canManageUsers(user: AuthUser | null): boolean {
  return isAdmin(user);
}

export function canEditUser(currentUser: AuthUser | null, targetUserId: string): boolean {
  if (!currentUser) return false;

  // Admins can edit any user, members can edit themselves
  return isAdmin(currentUser) || currentUser.id === targetUserId;
}

export function canDeleteUser(currentUser: AuthUser | null, targetUserId: string): boolean {
  if (!currentUser) return false;

  // Only admins can delete users, and they can't delete themselves
  return isAdmin(currentUser) && currentUser.id !== targetUserId;
}
