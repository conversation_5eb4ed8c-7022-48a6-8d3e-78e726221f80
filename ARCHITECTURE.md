# Architecture Overview

## MVVM Pattern Implementation

This application follows the Model-View-ViewModel (MVVM) architectural pattern with a feature-based folder structure.

### Model Layer (`/services`)

- **Purpose**: Data access, business logic, and external API communication
- **Location**: `src/features/*/services/`
- **Examples**:
  - `authService.ts` - Authentication logic, JWT handling
  - `userService.ts` - User CRUD operations, database interactions

### View Layer (`/components`)

- **Purpose**: UI presentation and user interaction
- **Location**: `src/features/*/components/` and `src/shared/components/`
- **Examples**:
  - `LoginForm.tsx` - Login form UI
  - `UserList.tsx` - User management interface
  - `Button.tsx` - Reusable UI component

### ViewModel Layer (`/hooks`)

- **Purpose**: State management, UI logic, and binding between Model and View
- **Location**: `src/features/*/hooks/`
- **Examples**:
  - `useAuth.ts` - Authentication state and actions
  - `useUsers.ts` - User management state and operations

## Feature-Based Structure

Each feature is self-contained and follows the same structure:

```
src/features/{feature-name}/
├── components/     # UI components specific to this feature
├── hooks/          # State management and business logic
├── services/       # Data access and API calls
└── pages/          # Page components (if needed)
```

### Benefits:

- **Scalability**: Easy to add new features without affecting existing ones
- **Maintainability**: Related code is co-located
- **Testability**: Each feature can be tested in isolation
- **Team Collaboration**: Different teams can work on different features

## Data Flow

```
User Interaction → View Component → ViewModel Hook → Model Service → Database/API
                                      ↓
User Interface ← View Component ← ViewModel Hook ← Model Service ← Response
```

### Example: User Login Flow

1. **User clicks login button** (View)
2. **LoginForm calls useAuth hook** (ViewModel)
3. **useAuth calls authService.login()** (Model)
4. **authService makes API call** (Model)
5. **API validates credentials and returns JWT** (Model)
6. **useAuth updates authentication state** (ViewModel)
7. **LoginForm re-renders with new state** (View)
8. **User is redirected to dashboard** (View)

## Security Architecture

### Authentication Flow

1. User credentials are validated against hashed passwords in database
2. JWT token is generated and stored in HTTP-only cookie
3. Middleware validates JWT on protected routes
4. Token is automatically included in API requests

### Authorization Levels

- **Public**: Home page, login, register
- **Member**: Dashboard, products, cart, profile
- **Admin**: User management, product management, transaction management

### Security Measures

- Password hashing with bcryptjs
- JWT tokens in HTTP-only cookies
- CSRF protection via SameSite cookies
- Input validation and sanitization
- Role-based access control
- Secure API endpoints with authentication checks

## Database Design

### User Model

```prisma
model User {
  id        String   @id @default(cuid())
  name      String
  email     String   @unique
  password  String   // Hashed with bcryptjs
  role      String   @default("member")
  createdAt DateTime @default(now())
}
```

### Design Decisions

- **CUID for IDs**: More secure than auto-incrementing integers
- **Unique email constraint**: Prevents duplicate accounts
- **Role-based system**: Extensible for future role additions
- **Timestamps**: Track user creation for auditing

## API Design

### RESTful Endpoints

- **GET /api/users** - List users (paginated)
- **POST /api/users** - Create user (admin only)
- **GET /api/users/[id]** - Get specific user
- **PUT /api/users/[id]** - Update user (admin only)
- **DELETE /api/users/[id]** - Delete user (admin only)

### Authentication Endpoints

- **POST /api/auth/login** - User login
- **POST /api/auth/register** - User registration
- **POST /api/auth/logout** - User logout
- **GET /api/auth/me** - Get current user

### Response Format

```json
{
  "data": {...},
  "message": "Success message",
  "error": null
}
```

## Testing Strategy

### End-to-End Testing (Playwright)

- **Page Object Model**: Reusable page classes for maintainable tests
- **Test Coverage**: Authentication, CRUD operations, navigation
- **Cross-browser**: Chrome, Firefox, Safari
- **CI/CD Ready**: Headless execution for automated testing

### Test Structure

```
tests/
├── pages/          # Page Object Model classes
├── setup/          # Test configuration and setup
├── auth.spec.ts    # Authentication tests
├── users.spec.ts   # User management tests
└── navigation.spec.ts # Navigation and routing tests
```

## Deployment Architecture

### Development

- Local PostgreSQL via Docker
- Next.js development server
- Hot reload for rapid development

### Production

- Containerized application with Docker
- PostgreSQL database with persistent volumes
- Environment-based configuration
- Health checks and restart policies

### Docker Services

- **postgres**: Database service with health checks
- **app**: Next.js application with standalone output
- **volumes**: Persistent data storage

## Performance Considerations

### Frontend Optimizations

- Next.js App Router for optimal loading
- Component lazy loading where appropriate
- Tailwind CSS for minimal bundle size
- Image optimization with Next.js Image component

### Backend Optimizations

- Prisma ORM for efficient database queries
- JWT tokens for stateless authentication
- HTTP-only cookies for security and performance
- Database indexing on frequently queried fields

### Caching Strategy

- Next.js automatic static optimization
- Browser caching for static assets
- Database query optimization with Prisma

## Scalability Considerations

### Horizontal Scaling

- Stateless authentication enables load balancing
- Database connection pooling
- CDN for static asset delivery
- Microservices-ready architecture

### Vertical Scaling

- Efficient database queries
- Optimized bundle sizes
- Memory-efficient React components
- Database indexing strategy

## Monitoring and Logging

### Application Monitoring

- Error boundaries for React components
- API error handling and logging
- Database query monitoring
- Performance metrics tracking

### Security Monitoring

- Failed login attempt tracking
- Suspicious activity detection
- Rate limiting implementation
- Audit logging for admin actions
