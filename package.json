{"name": "fullstack-nextjs-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "format": "prettier --write .", "format:check": "prettier --check .", "type-check": "tsc --noEmit", "setup": "npm install && npm run db:generate && npm run db:push && npm run db:seed", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:seed": "tsx prisma/seed.ts", "db:studio": "prisma studio", "db:reset": "prisma migrate reset --force", "db:clear-seed": "npx prisma migrate reset --force && npx prisma generate && npx tsx prisma/seed.ts", "db:seed-only": "npx tsx prisma/seed.ts", "db:fix": "npx prisma db push --force-reset --accept-data-loss && npx prisma generate && npx tsx prisma/seed.ts", "db:test": "node scripts/test-db-connection.js", "db:migrate-roles": "node scripts/migrate-user-role.js", "test:e2e": "playwright test", "test:e2e:ci": "cross-env CI=true playwright test", "test:e2e:ui": "playwright test --ui", "test:e2e:setup": "playwright test --project=setup", "test:e2e:unauthenticated": "playwright test --project=unauthenticated", "test:e2e:admin": "playwright test --project=admin", "test:e2e:member": "playwright test --project=member", "test:all": "npm run lint && npm run type-check && npm run build && npm run test:e2e", "prepare": "husky"}, "dependencies": {"@prisma/client": "^5.7.1", "bcryptjs": "^2.4.3", "jose": "^5.1.3", "next": "^15.3.3", "prisma": "^5.7.1", "react": "^18", "react-dom": "^18", "react-hot-toast": "^2.5.2", "zod": "^3.22.4"}, "devDependencies": {"@playwright/test": "^1.40.1", "@types/bcryptjs": "^2.4.6", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "autoprefixer": "^10.0.1", "cross-env": "^7.0.3", "eslint": "^8", "eslint-config-next": "^15.3.3", "husky": "^9.1.7", "lint-staged": "^16.1.0", "postcss": "^8", "prettier": "^3.5.3", "tailwindcss": "^3.3.0", "tsx": "^4.6.2", "typescript": "^5"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,md,yml,yaml}": ["prettier --write"], "*.{css,scss}": ["prettier --write"]}}