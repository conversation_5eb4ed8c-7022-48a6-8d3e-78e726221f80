'use client';

import React from 'react';
import { ProductList } from '@/features/products/components/ProductList';
import { useAuth } from '@/features/auth/hooks/useAuth';

export default function ProductsPage() {
  const { user } = useAuth();

  // Redirect if admin user
  if (user && user.role === 'admin') {
    return (
      <div className="text-center py-12">
        <div className="text-orange-600 mb-4">
          <svg className="mx-auto h-12 w-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
            />
          </svg>
        </div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">Access Restricted</h3>
        <p className="text-gray-600 mb-4">
          Admin users should use the Manage Products page for product management.
        </p>
        <a
          href="/admin/products"
          className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
        >
          Go to Manage Products
        </a>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="bg-white shadow-sm rounded-lg p-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Products</h1>
            <p className="text-gray-600 mt-1">Browse our collection of products</p>
          </div>
          <div className="flex items-center space-x-2">
            <div className="text-sm text-gray-500">
              <svg
                className="inline w-4 h-4 mr-1"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
              Only members can add items to cart
            </div>
          </div>
        </div>
      </div>

      {/* Products List */}
      <ProductList />
    </div>
  );
}
