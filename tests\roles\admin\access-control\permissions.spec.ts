import { test, expect } from '@playwright/test';
import { DashboardPage } from '../../../pages/DashboardPage';
import { UsersPage } from '../../../pages/UsersPage';

// Use admin storage state for all tests in this file
test.use({ storageState: 'tests/.auth/admin.json' });

test.describe('Admin - Access Control', () => {
  let dashboardPage: DashboardPage;
  let usersPage: UsersPage;

  test.beforeEach(async ({ page }) => {
    dashboardPage = new DashboardPage(page);
    usersPage = new UsersPage(page);
  });

  test('should have access to user management', async ({ page }) => {
    await dashboardPage.goto();

    // Admin should see "Users" in navigation sidebar
    const usersNavLink = page.locator('nav a:has-text("Users")');
    await expect(usersNavLink).toBeVisible();

    // Admin should see "Manage Users" button in dashboard
    const manageUsersLink = page.locator('text="Manage Users"');
    await expect(manageUsersLink).toBeVisible();

    // Should have multiple ways to access user management
    const userLinks = page.locator('a[href="/users"]');
    const linkCount = await userLinks.count();
    expect(linkCount).toBeGreaterThan(0);
  });

  test('should be able to access users page directly', async ({ page }) => {
    // Navigate directly to users page
    await page.goto('/users');
    await page.waitForLoadState('networkidle');

    // Should successfully load the users page
    await expect(page).toHaveURL('/users');
    await expect(page.locator('h1')).toContainText('Users');

    // Should see user management elements
    await expect(usersPage.userTable).toBeVisible();
    await expect(usersPage.createUserButton).toBeVisible();
  });

  test('should see admin-appropriate navigation items', async ({ page }) => {
    await dashboardPage.goto();

    // Admin should see these navigation items
    const usersLink = page.locator('nav a:has-text("Users")');
    const dashboardLink = page.locator('nav a:has-text("Dashboard")');

    await expect(usersLink).toBeVisible();
    await expect(dashboardLink).toBeVisible();

    // Admin should NOT see member-only shopping features
    const cartLink = page.locator('nav a:has-text("Cart")');
    const cartIcon = page.locator('[data-testid="cart-icon"]');

    await expect(cartLink).not.toBeVisible();
    await expect(cartIcon).not.toBeVisible();
  });

  test('should display correct user role in dashboard', async ({ page }) => {
    await dashboardPage.goto();

    // Check that the user role is displayed as "admin"
    const roleText = page
      .locator('text="Role:"')
      .locator('..')
      .or(page.locator('[data-testid="user-role"]'));

    await expect(roleText).toContainText('admin');
  });

  test('should be able to navigate to users page from dashboard', async ({ page }) => {
    await dashboardPage.goto();

    // Should be able to navigate to users page
    await dashboardPage.goToUsers();

    await expect(page).toHaveURL('/users');
    await expect(usersPage.userTable).toBeVisible();
  });

  test('should NOT have access to shopping cart', async ({ page }) => {
    // Admin should not be able to access cart functionality
    await page.goto('/cart');
    await page.waitForLoadState('networkidle');

    // Should see access restricted message, not the shopping cart
    await expect(page.locator('h3')).toContainText('Access Restricted');

    // Should not see normal cart functionality
    await expect(page.locator('h1:has-text("Shopping Cart")')).not.toBeVisible();
    await expect(page.locator('[data-testid="cart-item"]')).not.toBeVisible();
    await expect(page.locator('button:has-text("Checkout")')).not.toBeVisible();
  });
});
