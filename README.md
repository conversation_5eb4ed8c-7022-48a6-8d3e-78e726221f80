# FullStack Next.js Application

A modern full-stack web application built with Next.js 14, featuring authentication, user management, and a clean MVVM architecture.

## Tech Stack

- **Frontend & Backend**: Next.js 14 (App Router, TypeScript)
- **Architecture**: MVVM (Model-View-ViewModel) with feature-based structure
- **Database**: PostgreSQL with Prisma ORM
- **Authentication**: JWT with HTTP-only cookies
- **Styling**: Tailwind CSS
- **Testing**: Playwright with Page Object Model (POM)
- **Containerization**: Docker & Docker Compose
- **Linting**: ESLint with TypeScript support

## Features

### Authentication
- User registration with validation
- Secure login/logout
- JWT-based session management
- Password hashing with bcryptjs

### User Management
- List all users (with pagination-ready structure)
- Create new users with role assignment
- Edit existing user details
- Delete users with confirmation
- Role-based access control (Admin/User)

## Project Structure

```
src/
├── features/
│   ├── auth/
│   │   ├── components/     # Login/Register forms
│   │   ├── hooks/          # ViewModel layer (useAuth)
│   │   ├── services/       # Model layer (authService)
│   │   └── pages/          # Page components
│   ├── users/
│   │   ├── components/     # User list/form components
│   │   ├── hooks/          # ViewModel layer (useUsers)
│   │   ├── services/       # Model layer (userService)
│   │   └── pages/          # Page components
│   ├── products/
│   │   ├── components/     # Product components
│   │   ├── hooks/          # Product hooks
│   │   └── services/       # Product services
│   └── cart/
│       ├── components/     # Cart components
│       ├── hooks/          # Cart hooks
│       └── services/       # Cart services
├── shared/
│   ├── components/         # Reusable UI components
│   ├── hooks/              # Shared hooks
│   └── utils/              # Utility functions
└── app/
    ├── api/                # API routes
    │   ├── auth/           # Authentication endpoints
    │   ├── users/          # User management endpoints
    │   ├── products/       # Product endpoints
    │   ├── cart/           # Cart endpoints
    │   └── transactions/   # Transaction endpoints
    ├── (dashboard)/        # Route group (URLs without /dashboard)
    │   ├── page.tsx        # Dashboard home → /
    │   ├── admin/
    │   │   ├── products/   # Admin product management → /admin/products
    │   │   ├── transactions/ # Admin transactions → /admin/transactions
    │   │   └── users/      # Admin user management → /admin/users
    │   ├── cart/           # Member cart → /cart
    │   ├── orders/         # Member orders → /orders
    │   └── products/       # Member products → /products
    ├── login/              # Login page → /login
    ├── register/           # Register page → /register
    └── page.tsx            # Landing page → /
```

### Route Structure

The application uses Next.js App Router with a route group `(dashboard)` for clean URLs:

**Public Routes:**
- `/` - Landing page
- `/login` - User login
- `/register` - User registration

**Protected Routes (Member Role):**
- `/` - Dashboard home (route group default)
- `/products` - Browse products
- `/cart` - Shopping cart
- `/orders` - Order history

**Admin Routes (Admin Role Only):**
- `/admin/users` - User management
- `/admin/products` - Product management
- `/admin/transactions` - Transaction management

**Key Benefits of Route Group `(dashboard)`:**
- ✅ Clean URLs without `/dashboard` prefix
- ✅ Organized file structure
- ✅ Shared layouts and middleware
- ✅ Better user experience

## Getting Started

### Prerequisites
- Node.js 18+ 
- Docker & Docker Compose
- Git

### Quick Setup (Recommended)

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd fullstack-nextjs-app
   ```

2. **Run the setup script**
   ```bash
   # For Unix/Linux/macOS
   npm run setup

   # For Windows (Docker setup)
   setup-docker.bat
   ```

3. **Start development**
   ```bash
   npm run dev
   ```

4. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

### Manual Setup

If you prefer to set up manually:

1. **Install dependencies**
   ```bash
   npm install
   ```

2. **Set up environment variables**
   ```bash
   cp .env.local.example .env.local
   # Edit .env.local with your configuration
   ```

3. **Start the database**
   ```bash
   docker-compose up postgres -d
   ```

4. **Set up the database**
   ```bash
   npm run db:generate
   npm run db:push
   npm run db:seed
   ```

5. **Start the development server**
   ```bash
   npm run dev
   ```

### Using Docker (Full Stack)

1. **Start all services**
   ```bash
   docker-compose up -d
   ```

2. **Set up the database** (first time only)
   ```bash
   docker-compose exec app npm run db:push
   docker-compose exec app npm run db:seed
   ```

## Default Users

After seeding the database, you can use these accounts:

- **Admin User**
  - Email: `<EMAIL>`
  - Password: `password123`
  - Role: `admin`

- **Regular User**
  - Email: `<EMAIL>`
  - Password: `password123`
  - Role: `user`

## Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint
- `npm run db:generate` - Generate Prisma client
- `npm run db:push` - Push schema to database
- `npm run db:migrate` - Run database migrations
- `npm run db:seed` - Seed database with test data
- `npm run db:studio` - Open Prisma Studio
- `npm run test:e2e` - Run Playwright tests
- `npm run test:e2e:ui` - Run Playwright tests with UI

## Testing

### End-to-End Testing with Playwright

The application includes comprehensive E2E tests using Playwright with the Page Object Model pattern.

**GitHub Actions Integration:**
- Automated test execution on pull requests and pushes
- Test results displayed in PR comments
- Downloadable test reports with detailed results

**Run tests locally:**
```bash
npm run test:e2e
```

**Run tests with UI:**
```bash
npm run test:e2e:ui
```

**Test Coverage:**
- Authentication flow (login, register, logout)
- User CRUD operations
- Navigation and routing
- Form validation
- Role-based access control

### Continuous Integration

The project includes GitHub Actions for automated testing:

**🎭 Playwright Tests on Pull Requests**
- Automatically runs on every pull request
- Tests against PostgreSQL database
- Generates detailed HTML reports
- Comments test results directly on PRs
- Uploads test artifacts for download

**Features:**
- ✅ Automated test execution
- 📊 Test result summaries in PR comments
- 📋 Downloadable HTML reports
- 🔄 Retry failed tests automatically
- 📸 Screenshots and videos on failures

## API Endpoints

### Authentication
- `POST /api/auth/login` - User login
- `POST /api/auth/register` - User registration
- `POST /api/auth/logout` - User logout
- `GET /api/auth/me` - Get current user

### Users
- `GET /api/users` - List all users
- `POST /api/users` - Create new user (Admin only)
- `GET /api/users/[id]` - Get user by ID
- `PUT /api/users/[id]` - Update user (Admin only)
- `DELETE /api/users/[id]` - Delete user (Admin only)

## Architecture Patterns

### MVVM Implementation
- **Model**: Services layer (`/services`) - Data access and business logic
- **View**: Components layer (`/components`) - UI presentation
- **ViewModel**: Hooks layer (`/hooks`) - State management and UI logic

### Feature-Based Structure
Each feature is self-contained with its own:
- Components (UI)
- Hooks (State management)
- Services (Data layer)
- Types (TypeScript definitions)

## Security Features

- Password hashing with bcryptjs
- JWT tokens stored in HTTP-only cookies
- CSRF protection through SameSite cookies
- Input validation and sanitization
- Role-based access control
- Secure API endpoints

## Deployment

### Production Build
```bash
npm run build
npm start
```

### Docker Production
```bash
docker-compose -f docker-compose.prod.yml up -d
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new features
5. Run the test suite
6. Submit a pull request

## License

This project is licensed under the MIT License.
