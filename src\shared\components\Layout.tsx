'use client';

import React from 'react';
import Link from 'next/link';
import { ClientLayout } from './ClientLayout';

type LayoutProps = {
  children: React.ReactNode;
};

export const Layout: React.FC<LayoutProps> = ({ children }) => {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Top Navigation Bar - Sticky */}
      <nav className="bg-white shadow-sm border-b sticky top-0 z-50">
        <div className="flex justify-between h-16">
          <div className="flex items-center">
            <Link href="/" className="text-xl font-bold text-gray-900 ml-6">
              FullStack App
            </Link>
          </div>

          {/* Client-side navigation will be rendered here */}
          <div className="flex items-center mr-6">
            <ClientLayout isNavigation={true} />
          </div>
        </div>
      </nav>

      {/* Client-side layout handles the main content and sidebar */}
      <ClientLayout>{children}</ClientLayout>
    </div>
  );
};
