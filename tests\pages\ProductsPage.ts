import { Page, Locator } from '@playwright/test';

export class ProductsPage {
  readonly page: Page;
  readonly productCards: Locator;
  readonly addToCartButtons: Locator;
  readonly cartIcon: Locator;
  readonly cartCount: Locator;
  readonly searchInput: Locator;
  readonly searchButton: Locator;
  readonly categorySelect: Locator;
  readonly clearFiltersButton: Locator;

  constructor(page: Page) {
    this.page = page;
    this.productCards = page
      .locator('[data-testid="product-card"]')
      .or(page.locator('.bg-white.rounded-lg.shadow-md'));
    this.addToCartButtons = page.locator('button:has-text("Add to Cart")');
    this.cartIcon = page.locator('[data-testid="cart-icon"]').first();
    this.cartCount = page.locator('[data-testid="cart-count"]').first();
    this.searchInput = page.locator('input[placeholder*="Search"]');
    this.searchButton = page.locator('button:has-text("Search")');
    this.categorySelect = page
      .locator('select')
      .or(page.locator('[data-testid="category-select"]'));
    this.clearFiltersButton = page.locator('button:has-text("Clear Filters")');
  }

  async goto() {
    await this.page.goto('/products');
    await this.page.waitForLoadState('networkidle');
  }

  async findAvailableProductIndex(): Promise<number> {
    const productCount = await this.getProductsCount();

    for (let i = 0; i < productCount; i++) {
      const productCard = this.productCards.nth(i);
      const outOfStockButton = productCard.locator('button:has-text("Out of Stock")');
      const addToCartButton = productCard.locator('button:has-text("Add to Cart")');

      // Check if this product has an "Add to Cart" button and is not out of stock
      if (
        (await addToCartButton.isVisible({ timeout: 1000 })) &&
        !(await outOfStockButton.isVisible({ timeout: 500 }))
      ) {
        return i;
      }
    }

    throw new Error('No available products found with "Add to Cart" button');
  }

  async addToCart(productIndex: number) {
    const productCard = this.productCards.nth(productIndex);

    // First check if product is out of stock
    const outOfStockButton = productCard.locator('button:has-text("Out of Stock")');
    if (await outOfStockButton.isVisible({ timeout: 1000 })) {
      throw new Error(`Product at index ${productIndex} is out of stock`);
    }

    // Look for Add to Cart button
    const addToCartButton = productCard.locator('button:has-text("Add to Cart")');

    // Wait for button to be visible and clickable
    await addToCartButton.waitFor({ state: 'visible', timeout: 10000 });
    await addToCartButton.click();

    // Wait for the request to complete
    try {
      await this.page.waitForResponse(
        response => response.url().includes('/api/cart') && response.request().method() === 'POST',
        { timeout: 10000 }
      );
    } catch (error) {
      console.log('Cart API response not detected, continuing...');
      // Wait for UI update instead
      await this.page.waitForTimeout(1000);
    }
  }

  async addAvailableProductToCart(): Promise<number> {
    const availableIndex = await this.findAvailableProductIndex();
    await this.addToCart(availableIndex);
    return availableIndex;
  }

  async addMultipleDifferentProductsToCart(count: number = 3): Promise<number[]> {
    const addedIndices: number[] = [];
    const productCount = await this.getProductsCount();

    for (let i = 0; i < productCount && addedIndices.length < count; i++) {
      const productCard = this.productCards.nth(i);
      const outOfStockButton = productCard.locator('button:has-text("Out of Stock")');
      const addToCartButton = productCard.locator('button:has-text("Add to Cart")');

      // Check if this product has an "Add to Cart" button and is not out of stock
      if (
        (await addToCartButton.isVisible({ timeout: 1000 })) &&
        !(await outOfStockButton.isVisible({ timeout: 500 }))
      ) {
        await this.addToCart(i);
        addedIndices.push(i);
        await this.page.waitForTimeout(500); // Small delay between additions
      }
    }

    if (addedIndices.length === 0) {
      throw new Error('No available products found');
    }

    return addedIndices;
  }

  async getProductName(productIndex: number): Promise<string> {
    const productCard = this.productCards.nth(productIndex);
    const nameElement = productCard.locator('h3').first();
    return (await nameElement.textContent()) || '';
  }

  async getProductPrice(productIndex: number): Promise<string> {
    const productCard = this.productCards.nth(productIndex);
    const priceElement = productCard.locator('text=/\\$\\d+\\.\\d{2}/').first();
    const priceText = (await priceElement.textContent()) || '$0.00';
    return priceText.replace('$', '');
  }

  async getProductStock(productIndex: number): Promise<string> {
    const productCard = this.productCards.nth(productIndex);
    const stockElement = productCard.locator('text=/\\d+ in stock/').first();
    const stockText = (await stockElement.textContent()) || '0 in stock';
    return stockText.replace(' in stock', '');
  }

  async searchProducts(searchTerm: string) {
    await this.searchInput.fill(searchTerm);
    await this.searchButton.click();

    // Wait for search results
    await this.page.waitForResponse(
      response => response.url().includes('/api/products') && response.url().includes('search=')
    );
  }

  async selectCategory(category: string) {
    await this.categorySelect.selectOption(category);

    // Wait for filter results
    await this.page.waitForResponse(
      response => response.url().includes('/api/products') && response.url().includes('category=')
    );
  }

  async clearFilters() {
    if (await this.clearFiltersButton.isVisible()) {
      await this.clearFiltersButton.click();

      // Wait for unfiltered results
      await this.page.waitForResponse(response => response.url().includes('/api/products'));
    }
  }

  async getCartCount(): Promise<number> {
    if (await this.cartCount.isVisible()) {
      const countText = (await this.cartCount.textContent()) || '0';
      const count = countText.replace('+', '');
      return parseInt(count);
    }
    return 0;
  }

  async waitForProducts() {
    await this.productCards.first().waitFor({ state: 'visible', timeout: 10000 });
  }

  async getProductsCount(): Promise<number> {
    return await this.productCards.count();
  }

  async isProductOutOfStock(productIndex: number): Promise<boolean> {
    const productCard = this.productCards.nth(productIndex);
    const outOfStockText = productCard.locator('text="Out of stock"');
    return await outOfStockText.isVisible();
  }

  async isAddToCartDisabled(productIndex: number): Promise<boolean> {
    const productCard = this.productCards.nth(productIndex);
    const addToCartButton = productCard.locator('button:has-text("Add to Cart")');

    if (await addToCartButton.isVisible()) {
      return await addToCartButton.isDisabled();
    }

    // If button shows "Out of Stock" instead
    const outOfStockButton = productCard.locator('button:has-text("Out of Stock")');
    return await outOfStockButton.isVisible();
  }

  async getProductCategory(productIndex: number): Promise<string> {
    const productCard = this.productCards.nth(productIndex);
    const categoryBadge = productCard.locator('.bg-blue-100').first();

    if (await categoryBadge.isVisible()) {
      return (await categoryBadge.textContent()) || '';
    }
    return '';
  }

  async hasFloatingAnimation(): Promise<boolean> {
    const animation = this.page.locator('[data-testid="floating-animation"]');
    return await animation.isVisible();
  }

  async waitForFloatingAnimationToComplete() {
    const animation = this.page.locator('[data-testid="floating-animation"]');

    // Wait for animation to appear
    await animation.waitFor({ state: 'visible', timeout: 2000 });

    // Wait for animation to disappear
    await animation.waitFor({ state: 'hidden', timeout: 3000 });
  }

  async navigateToPage(pageNumber: number) {
    const pageButton = this.page.locator(`button:has-text("${pageNumber}")`);
    await pageButton.click();

    // Wait for page to load
    await this.page.waitForResponse(
      response =>
        response.url().includes('/api/products') && response.url().includes(`page=${pageNumber}`)
    );
  }

  async goToNextPage() {
    const nextButton = this.page.locator('button:has-text("Next")');
    await nextButton.click();

    // Wait for next page to load
    await this.page.waitForResponse(response => response.url().includes('/api/products'));
  }

  async goToPreviousPage() {
    const prevButton = this.page.locator('button:has-text("Previous")');
    await prevButton.click();

    // Wait for previous page to load
    await this.page.waitForResponse(response => response.url().includes('/api/products'));
  }

  async isNextPageDisabled(): Promise<boolean> {
    const nextButton = this.page.locator('button:has-text("Next")');
    return await nextButton.isDisabled();
  }

  async isPreviousPageDisabled(): Promise<boolean> {
    const prevButton = this.page.locator('button:has-text("Previous")');
    return await prevButton.isDisabled();
  }
}
