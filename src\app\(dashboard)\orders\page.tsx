import React from 'react';
import { TransactionList } from '@/features/transactions/components/TransactionList';

export default function OrdersPage() {
  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="bg-white shadow-sm rounded-lg p-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">My Orders</h1>
            <p className="text-gray-600 mt-1">View your order history and track your purchases</p>
          </div>
          <div className="flex items-center space-x-2">
            <div className="text-sm text-gray-500">
              <svg
                className="inline w-4 h-4 mr-1"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
              Your personal order history
            </div>
          </div>
        </div>
      </div>

      {/* Orders List */}
      <TransactionList showUserInfo={false} />
    </div>
  );
}
