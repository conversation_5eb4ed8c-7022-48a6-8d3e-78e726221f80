import { NextResponse } from 'next/server';
import { getCartItemCount } from '@/features/cart/services/cartService';
import { getCurrentUser } from '@/features/auth/services/authService';

export async function GET() {
  try {
    const currentUser = await getCurrentUser();

    if (!currentUser) {
      return NextResponse.json({ count: 0 });
    }

    const count = await getCartItemCount(currentUser.id);
    return NextResponse.json({ count: Math.min(count, 99) }); // Max 99 for display
  } catch (error: any) {
    return NextResponse.json(
      { message: error.message || 'Failed to fetch cart count' },
      { status: 500 }
    );
  }
}
