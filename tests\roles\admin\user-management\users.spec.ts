import { test, expect } from '@playwright/test';
import { UsersPage } from '../../../pages/UsersPage';
import { DashboardPage } from '../../../pages/DashboardPage';
import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcryptjs';

let prisma: PrismaClient;

// Use admin storage state for all tests in this file
test.use({ storageState: 'tests/.auth/admin.json' });

test.describe('Admin - User Management', () => {
  test.beforeAll(async () => {
    // Initialize Prisma client
    prisma = new PrismaClient();
  });

  test.afterAll(async () => {
    if (prisma) {
      await prisma.$disconnect();
    }
  });

  test('should display users list', async ({ page }) => {
    const usersPage = new UsersPage(page);

    await usersPage.goto();

    // Should show the users table
    await expect(usersPage.userTable).toBeVisible();
    await expect(usersPage.createUserButton).toBeVisible();

    // Should have at least the seeded users
    const userCount = await usersPage.getUserCount();
    expect(userCount).toBeGreaterThanOrEqual(2);
  });

  test('should create a new user', async ({ page }) => {
    const usersPage = new UsersPage(page);
    const timestamp = Date.now();
    const testEmail = `newuser${timestamp}@example.com`;

    // Clean up any existing test user before starting
    await prisma.user.deleteMany({
      where: {
        email: testEmail,
      },
    });

    await usersPage.goto();

    await usersPage.createUser('New Test User', testEmail, 'password123', 'member');

    // Should see the new user in the table
    const userRow = usersPage.getUserRow(testEmail);
    await expect(userRow).toBeVisible();
    await expect(userRow).toContainText('New Test User');
    await expect(userRow).toContainText('member');

    // Clean up test user from database after test
    await prisma.user.deleteMany({
      where: {
        email: testEmail,
      },
    });
  });

  test('should edit an existing user', async ({ page }) => {
    const usersPage = new UsersPage(page);
    const timestamp = Date.now();
    const testEmail = `edituser${timestamp}@example.com`;

    // Clean up any existing test user before starting
    await prisma.user.deleteMany({
      where: {
        email: testEmail,
      },
    });

    // Seed test user directly in database
    await prisma.user.create({
      data: {
        name: 'User To Edit',
        email: testEmail,
        password: await bcrypt.hash('password123', 10),
        role: 'member',
      },
    });

    await usersPage.goto();

    // Now edit the user
    await usersPage.editUser(testEmail, 'Edited User Name');

    // Should see the updated name
    const userRow = usersPage.getUserRow(testEmail);
    await expect(userRow).toHaveText(/Edited User Name/);

    // Clean up test user from database after test
    await prisma.user.deleteMany({
      where: {
        email: testEmail,
      },
    });
  });

  test('should delete a user', async ({ page }) => {
    const usersPage = new UsersPage(page);
    const timestamp = Date.now();
    const testEmail = `deleteuser${timestamp}@example.com`;

    // Clean up any existing test user before starting
    await prisma.user.deleteMany({
      where: {
        email: testEmail,
      },
    });

    // Seed test user directly in database
    await prisma.user.create({
      data: {
        name: 'User To Delete',
        email: testEmail,
        password: await bcrypt.hash('password123', 10),
        role: 'member',
      },
    });

    await usersPage.goto();

    // Verify user exists in the UI
    let userRow = usersPage.getUserRow(testEmail);
    await expect(userRow).toBeVisible();

    // Delete the user
    page.on('dialog', dialog => dialog.accept()); // Accept confirmation dialog
    await usersPage.deleteUser(testEmail);

    // User should no longer exist in UI
    userRow = usersPage.getUserRow(testEmail);
    await expect(userRow).not.toBeVisible();

    // Clean up test user from database after test
    await prisma.user.deleteMany({
      where: {
        email: testEmail,
      },
    });
  });

  test('should create admin user', async ({ page }) => {
    const usersPage = new UsersPage(page);
    const timestamp = Date.now();
    const testEmail = `admin${timestamp}@example.com`;

    // Clean up any existing test user before starting
    await prisma.user.deleteMany({
      where: {
        email: testEmail,
      },
    });

    await usersPage.goto();

    await usersPage.createUser('New Admin User', testEmail, 'password123', 'admin');

    // Should see the new admin user with admin role
    const userRow = usersPage.getUserRow(testEmail);
    await expect(userRow).toBeVisible();
    await expect(userRow).toContainText('New Admin User');
    await expect(userRow).toContainText('admin');

    // Clean up test user from database after test
    await prisma.user.deleteMany({
      where: {
        email: testEmail,
      },
    });
  });

  test('should navigate from dashboard to users page', async ({ page }) => {
    const dashboardPage = new DashboardPage(page);
    const usersPage = new UsersPage(page);

    await dashboardPage.goto();
    await dashboardPage.goToUsers();

    await expect(page).toHaveURL('/users');
    await expect(usersPage.userTable).toBeVisible();
  });
});
