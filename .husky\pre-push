#!/usr/bin/env sh
. "$(dirname -- "$0")/_/husky.sh"

echo "🚀 Running pre-push checks..."

# 1. Run full test suite before pushing
echo "🧪 Running full test suite..."
npm run test:e2e
if [ $? -ne 0 ]; then
  echo "❌ Tests failed. Please fix failing tests before pushing."
  exit 1
fi
echo "✅ All tests passed"

# 2. Ensure build is successful
echo "🏗️ Running production build..."
npm run build
if [ $? -ne 0 ]; then
  echo "❌ Production build failed. Please fix build errors before pushing."
  exit 1
fi
echo "✅ Production build successful"

# 3. Run final lint check
echo "📝 Running final lint check..."
npm run lint
if [ $? -ne 0 ]; then
  echo "❌ <PERSON><PERSON> failed. Please fix linting errors before pushing."
  exit 1
fi
echo "✅ <PERSON><PERSON> passed"

echo "🎉 All pre-push checks passed! Safe to push to remote repository."
