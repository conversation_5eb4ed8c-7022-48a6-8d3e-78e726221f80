const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function migrateUserRoles() {
  try {
    console.log('🔄 Starting user role migration...');

    // Check if there are any users with 'user' role
    const usersWithUserRole = await prisma.user.findMany({
      where: {
        role: 'user',
      },
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
      },
    });

    if (usersWithUserRole.length === 0) {
      console.log('✅ No users found with "user" role. Migration not needed.');
      return;
    }

    console.log(`📊 Found ${usersWithUserRole.length} users with "user" role:`);
    usersWithUserRole.forEach(user => {
      console.log(`   - ${user.name} (${user.email})`);
    });

    // Update all users with 'user' role to 'member' role
    const updateResult = await prisma.user.updateMany({
      where: {
        role: 'user',
      },
      data: {
        role: 'member',
      },
    });

    console.log(
      `✅ Successfully migrated ${updateResult.count} users from "user" role to "member" role.`
    );

    // Verify the migration
    const remainingUserRoleUsers = await prisma.user.count({
      where: {
        role: 'user',
      },
    });

    if (remainingUserRoleUsers === 0) {
      console.log('✅ Migration verification successful: No users with "user" role remain.');
    } else {
      console.log(`⚠️  Warning: ${remainingUserRoleUsers} users still have "user" role.`);
    }

    // Show current role distribution
    const roleDistribution = await prisma.user.groupBy({
      by: ['role'],
      _count: {
        role: true,
      },
    });

    console.log('\n📊 Current user role distribution:');
    roleDistribution.forEach(group => {
      console.log(`   - ${group.role}: ${group._count.role} users`);
    });
  } catch (error) {
    console.error('❌ Error during migration:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the migration
migrateUserRoles().catch(error => {
  console.error('Migration failed:', error);
  process.exit(1);
});
