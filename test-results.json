{"config": {"configFile": "D:\\Coding\\Learn-AI\\Playwright-AI-E2E\\playwright.config.ts", "rootDir": "D:/Coding/Learn-AI/Playwright-AI-E2E", "forbidOnly": true, "fullyParallel": true, "globalSetup": "D:\\Coding\\Learn-AI\\Playwright-AI-E2E\\tests\\config\\auth.setup.js", "globalTeardown": null, "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {"actualWorkers": 1}, "preserveOutput": "always", "reporter": [["html", null], ["github", null], ["json", {"outputFile": "test-results.json"}]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "D:/Coding/Learn-AI/Playwright-AI-E2E/test-results", "repeatEach": 1, "retries": 2, "metadata": {"actualWorkers": 1}, "id": "setup", "name": "setup", "testDir": "D:/Coding/Learn-AI/Playwright-AI-E2E", "testIgnore": [], "testMatch": ["**/config/auth.setup.test.js"], "timeout": 90000}, {"outputDir": "D:/Coding/Learn-AI/Playwright-AI-E2E/test-results", "repeatEach": 1, "retries": 2, "metadata": {"actualWorkers": 1}, "id": "unauthenticated", "name": "unauthenticated", "testDir": "D:/Coding/Learn-AI/Playwright-AI-E2E/tests/roles/unauthenticated", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 90000}, {"outputDir": "D:/Coding/Learn-AI/Playwright-AI-E2E/test-results", "repeatEach": 1, "retries": 2, "metadata": {"actualWorkers": 1}, "id": "admin", "name": "admin", "testDir": "D:/Coding/Learn-AI/Playwright-AI-E2E/tests/roles/admin", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 90000}, {"outputDir": "D:/Coding/Learn-AI/Playwright-AI-E2E/test-results", "repeatEach": 1, "retries": 2, "metadata": {"actualWorkers": 1}, "id": "member", "name": "member", "testDir": "D:/Coding/Learn-AI/Playwright-AI-E2E/tests/roles/member", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 90000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.53.0", "workers": 2, "webServer": null}, "suites": [{"title": "tests\\roles\\unauthenticated\\auth.spec.ts", "file": "tests/roles/unauthenticated/auth.spec.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "Authentication - Unauthenticated Users", "file": "tests/roles/unauthenticated/auth.spec.ts", "line": 6, "column": 6, "specs": [{"title": "should register a new user successfully", "ok": true, "tags": [], "tests": [{"timeout": 90000, "annotations": [], "expectedStatus": "passed", "projectId": "unauthenticated", "projectName": "unauthenticated", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 1094, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-24T11:10:28.068Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "d748ac400d08b85935ef-1b0ea5dfec26efd86c66", "file": "tests/roles/unauthenticated/auth.spec.ts", "line": 7, "column": 7}]}]}], "errors": [], "stats": {"startTime": "2025-06-24T11:10:24.483Z", "duration": 4820.897, "expected": 1, "skipped": 0, "unexpected": 0, "flaky": 0}}