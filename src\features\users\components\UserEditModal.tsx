'use client';

import React from 'react';
import { Modal } from '@/shared/components/Modal';
import { UserForm } from './UserForm';
import { User } from '../services/userService';

type UserEditModalProps = {
  isOpen: boolean;
  onClose: () => void;
  user: User | null;
  onSuccess?: () => void;
};

export const UserEditModal: React.FC<UserEditModalProps> = ({
  isOpen,
  onClose,
  user,
  onSuccess,
}) => {
  const handleSubmit = () => {
    onClose();
    if (onSuccess) {
      onSuccess();
    }
  };

  const handleCancel = () => {
    onClose();
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} title={user ? 'Edit User' : 'Create User'} size="md">
      <UserForm user={user || undefined} onSubmit={handleSubmit} onCancel={handleCancel} />
    </Modal>
  );
};
