version: '3.8'

services:
  postgres:
    image: postgres:15
    container_name: fullstack-postgres-prod
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-secure_password_change_me}
      POSTGRES_DB: fullstack_app
    ports:
      - "5432:5432"
    volumes:
      - postgres_prod_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped

  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: fullstack-app-prod
    ports:
      - "3000:3000"
    environment:
      NODE_ENV: production
      DATABASE_URL: "postgresql://postgres:${POSTGRES_PASSWORD:-secure_password_change_me}@postgres:5432/fullstack_app?schema=public"
      JWT_SECRET: ${JWT_SECRET:-change-this-in-production}
      NEXTAUTH_URL: ${NEXTAUTH_URL:-http://localhost:3000}
    depends_on:
      postgres:
        condition: service_healthy
    restart: unless-stopped

volumes:
  postgres_prod_data:
