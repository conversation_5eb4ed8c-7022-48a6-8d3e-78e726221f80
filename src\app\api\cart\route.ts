import { NextRequest, NextResponse } from 'next/server';
import { getCartItems, addToCart, clearCart } from '@/features/cart/services/cartService';
import { getCurrentUser } from '@/features/auth/services/authService';

export async function GET() {
  try {
    const currentUser = await getCurrentUser();

    if (!currentUser) {
      return NextResponse.json({ message: 'Not authenticated' }, { status: 401 });
    }

    const cartSummary = await getCartItems(currentUser.id);
    return NextResponse.json(cartSummary);
  } catch (error: any) {
    return NextResponse.json(
      { message: error.message || 'Failed to fetch cart items' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const currentUser = await getCurrentUser();

    if (!currentUser) {
      return NextResponse.json({ message: 'Not authenticated' }, { status: 401 });
    }

    // Only members can add to cart
    if (currentUser.role !== 'member' && currentUser.role !== 'admin') {
      return NextResponse.json(
        { message: 'Member access required to add items to cart' },
        { status: 403 }
      );
    }

    const { productId, quantity = 1 } = await request.json();

    if (!productId) {
      return NextResponse.json({ message: 'Product ID is required' }, { status: 400 });
    }

    if (quantity <= 0) {
      return NextResponse.json({ message: 'Quantity must be greater than 0' }, { status: 400 });
    }

    const cartItem = await addToCart(currentUser.id, productId, quantity);
    return NextResponse.json(cartItem, { status: 201 });
  } catch (error: any) {
    return NextResponse.json(
      { message: error.message || 'Failed to add item to cart' },
      { status: 400 }
    );
  }
}

export async function DELETE() {
  try {
    const currentUser = await getCurrentUser();

    if (!currentUser) {
      return NextResponse.json({ message: 'Not authenticated' }, { status: 401 });
    }

    await clearCart(currentUser.id);
    return NextResponse.json({ message: 'Cart cleared successfully' });
  } catch (error: any) {
    return NextResponse.json({ message: error.message || 'Failed to clear cart' }, { status: 500 });
  }
}
