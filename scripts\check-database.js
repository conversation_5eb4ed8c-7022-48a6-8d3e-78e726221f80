const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function checkDatabase() {
  try {
    console.log('🔍 Checking database connection...');

    // Test connection
    await prisma.$connect();
    console.log('✅ Database connection successful');

    // Check users
    const users = await prisma.user.findMany();
    console.log(`\n👥 Users in database: ${users.length}`);
    users.forEach(user => {
      console.log(`  - ${user.name} (${user.email}) - Role: ${user.role}`);
    });

    // Check products
    const products = await prisma.product.findMany();
    console.log(`\n📦 Products in database: ${products.length}`);
    products.forEach(product => {
      console.log(`  - ${product.name} - $${product.price} - Stock: ${product.stock}`);
    });

    // Check transactions
    const transactions = await prisma.transaction.findMany();
    console.log(`\n💳 Transactions in database: ${transactions.length}`);

    // Check cart items
    const cartItems = await prisma.cartItem.findMany();
    console.log(`\n🛒 Cart items in database: ${cartItems.length}`);

    console.log('\n✅ Database check completed successfully');
  } catch (error) {
    console.error('❌ Database check failed:', error.message);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

checkDatabase();
