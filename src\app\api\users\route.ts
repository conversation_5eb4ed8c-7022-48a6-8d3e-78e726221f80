import { NextRequest, NextResponse } from 'next/server';
import { getUsers, createUser } from '@/features/users/services/userService';
import { getCurrentUser } from '@/features/auth/services/authService';

export async function GET() {
  try {
    const currentUser = await getCurrentUser();

    if (!currentUser) {
      return NextResponse.json({ message: 'Not authenticated' }, { status: 401 });
    }

    // Only allow admin to view users
    if (currentUser.role !== 'admin') {
      return NextResponse.json({ message: 'Access denied. Admin role required.' }, { status: 403 });
    }

    const users = await getUsers();
    return NextResponse.json(users);
  } catch (error: any) {
    return NextResponse.json(
      { message: error.message || 'Failed to fetch users' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const currentUser = await getCurrentUser();

    if (!currentUser || currentUser.role !== 'admin') {
      return NextResponse.json({ message: 'Admin access required' }, { status: 403 });
    }

    const userData = await request.json();
    const user = await createUser(userData);

    return NextResponse.json(user);
  } catch (error: any) {
    return NextResponse.json(
      { message: error.message || 'Failed to create user' },
      { status: 400 }
    );
  }
}
