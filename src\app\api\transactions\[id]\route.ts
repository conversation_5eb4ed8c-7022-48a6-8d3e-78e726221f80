import { NextRequest, NextResponse } from 'next/server';
import {
  getTransactionById,
  updateTransactionStatus,
} from '@/features/transactions/services/transactionService';
import { getCurrentUser } from '@/features/auth/services/authService';

export async function GET(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    const currentUser = await getCurrentUser();

    if (!currentUser) {
      return NextResponse.json({ message: 'Not authenticated' }, { status: 401 });
    }

    const { id } = await params;
    const transaction = await getTransactionById(id);

    if (!transaction) {
      return NextResponse.json({ message: 'Transaction not found' }, { status: 404 });
    }

    // Users can only see their own transactions, admins can see all
    if (currentUser.role !== 'admin' && transaction.userId !== currentUser.id) {
      return NextResponse.json({ message: 'Access denied' }, { status: 403 });
    }

    return NextResponse.json(transaction);
  } catch (error: any) {
    return NextResponse.json(
      { message: error.message || 'Failed to fetch transaction' },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    const currentUser = await getCurrentUser();

    if (!currentUser || currentUser.role !== 'admin') {
      return NextResponse.json({ message: 'Admin access required' }, { status: 403 });
    }

    const { status } = await request.json();
    const { id } = await params;

    if (!['pending', 'completed', 'cancelled'].includes(status)) {
      return NextResponse.json(
        { message: 'Invalid status. Must be pending, completed, or cancelled' },
        { status: 400 }
      );
    }

    const transaction = await updateTransactionStatus(id, status);
    return NextResponse.json(transaction);
  } catch (error: any) {
    return NextResponse.json(
      { message: error.message || 'Failed to update transaction' },
      { status: 400 }
    );
  }
}
