import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

// Test-only endpoint for clearing cart by email
export async function POST(request: NextRequest) {
  try {
    // Only allow in development/test environment
    if (process.env.NODE_ENV === 'production') {
      return NextResponse.json({ message: 'Not available in production' }, { status: 403 });
    }

    const { email } = await request.json();

    if (!email) {
      return NextResponse.json({ message: 'Email is required' }, { status: 400 });
    }

    // Find user by email
    const user = await prisma.user.findUnique({
      where: { email },
    });

    if (!user) {
      return NextResponse.json({ message: 'User not found' }, { status: 404 });
    }

    // Clear all cart items for this user
    const deletedItems = await prisma.cartItem.deleteMany({
      where: { userId: user.id },
    });

    return NextResponse.json({
      message: 'Cart cleared successfully',
      deletedCount: deletedItems.count,
    });
  } catch (error: any) {
    return NextResponse.json({ message: error.message || 'Failed to clear cart' }, { status: 500 });
  }
}
