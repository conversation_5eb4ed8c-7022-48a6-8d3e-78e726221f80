'use client';

import { useState, useEffect, useCallback } from 'react';
import { CartSummary } from '../services/cartService';

export function useCart() {
  const [cart, setCart] = useState<CartSummary>({
    items: [],
    totalItems: 0,
    totalAmount: 0,
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchCart = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch('/api/cart');

      if (!response.ok) {
        if (response.status === 401) {
          // User not authenticated, return empty cart
          setCart({ items: [], totalItems: 0, totalAmount: 0 });
          return;
        }
        throw new Error('Failed to fetch cart');
      }

      const data = await response.json();
      setCart(data);
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchCart();
  }, [fetchCart]);

  const addToCart = async (productId: string, quantity: number = 1) => {
    try {
      setError(null);

      const response = await fetch('/api/cart', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ productId, quantity }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to add item to cart');
      }

      const result = await response.json();

      // Refresh cart after adding item
      await fetchCart();

      // Dispatch custom event to notify other components
      window.dispatchEvent(new CustomEvent('cartUpdated'));

      return result;
    } catch (err: any) {
      setError(err.message);
      throw err;
    }
  };

  const updateQuantity = async (cartItemId: string, quantity: number) => {
    try {
      setError(null);

      // Optimistic update - update UI immediately
      setCart(prevCart => {
        const updatedItems = prevCart.items.map(item => {
          if (item.id === cartItemId) {
            const updatedItem = { ...item, quantity };
            return updatedItem;
          }
          return item;
        });

        // Recalculate totals
        const totalItems = updatedItems.reduce((sum, item) => sum + item.quantity, 0);
        const totalAmount = updatedItems.reduce(
          (sum, item) => sum + item.quantity * item.product.price,
          0
        );

        return {
          items: updatedItems,
          totalItems,
          totalAmount,
        };
      });

      const response = await fetch(`/api/cart/${cartItemId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ quantity }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        // Revert optimistic update on error
        await fetchCart();
        throw new Error(errorData.message || 'Failed to update quantity');
      }

      // Dispatch custom event to notify other components
      window.dispatchEvent(new CustomEvent('cartUpdated'));

      return await response.json();
    } catch (err: any) {
      setError(err.message);
      throw err;
    }
  };

  const removeItem = async (cartItemId: string) => {
    try {
      setError(null);

      // Optimistic update - remove item from UI immediately
      setCart(prevCart => {
        const updatedItems = prevCart.items.filter(item => item.id !== cartItemId);

        // Recalculate totals
        const totalItems = updatedItems.reduce((sum, item) => sum + item.quantity, 0);
        const totalAmount = updatedItems.reduce(
          (sum, item) => sum + item.quantity * item.product.price,
          0
        );

        return {
          items: updatedItems,
          totalItems,
          totalAmount,
        };
      });

      const response = await fetch(`/api/cart/${cartItemId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json();
        // Revert optimistic update on error
        await fetchCart();
        throw new Error(errorData.message || 'Failed to remove item');
      }

      // Dispatch custom event to notify other components
      window.dispatchEvent(new CustomEvent('cartUpdated'));
    } catch (err: any) {
      setError(err.message);
      throw err;
    }
  };

  const clearCart = async () => {
    try {
      setError(null);

      const response = await fetch('/api/cart', {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to clear cart');
      }

      // Refresh cart after clearing
      await fetchCart();

      // Dispatch custom event to notify other components
      window.dispatchEvent(new CustomEvent('cartUpdated'));
    } catch (err: any) {
      setError(err.message);
      throw err;
    }
  };

  const checkout = async () => {
    try {
      setError(null);

      const response = await fetch('/api/transactions', {
        method: 'POST',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to checkout');
      }

      // Refresh cart after checkout (should be empty)
      await fetchCart();

      // Dispatch custom event to notify other components
      window.dispatchEvent(new CustomEvent('cartUpdated'));

      return await response.json();
    } catch (err: any) {
      setError(err.message);
      throw err;
    }
  };

  return {
    cart,
    loading,
    error,
    addToCart,
    updateQuantity,
    removeItem,
    clearCart,
    checkout,
    refetch: fetchCart,
  };
}

export function useCartCount() {
  const [count, setCount] = useState(0);
  const [loading, setLoading] = useState(true);

  const fetchCount = useCallback(async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/cart/count');

      if (response.ok) {
        const data = await response.json();
        setCount(data.count);
      } else {
        setCount(0);
      }
    } catch (err) {
      setCount(0);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchCount();
  }, [fetchCount]);

  return {
    count,
    loading,
    refetch: fetchCount,
  };
}
