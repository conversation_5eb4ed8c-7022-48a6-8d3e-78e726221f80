'use client';

import React, { useState, useEffect } from 'react';

interface FloatingCartAnimationProps {
  isVisible: boolean;
  productImage?: string;
  productName?: string;
  onAnimationComplete: () => void;
}

export const FloatingCartAnimation: React.FC<FloatingCartAnimationProps> = ({
  isVisible,
  productImage,
  productName,
  onAnimationComplete,
}) => {
  const [isAnimating, setIsAnimating] = useState(false);

  useEffect(() => {
    if (isVisible) {
      setIsAnimating(true);

      // Animation duration
      const timer = setTimeout(() => {
        setIsAnimating(false);
        onAnimationComplete();
      }, 1000);

      return () => clearTimeout(timer);
    }
  }, [isVisible, onAnimationComplete]);

  if (!isVisible && !isAnimating) return null;

  return (
    <div className="fixed inset-0 pointer-events-none z-50" data-testid="floating-animation">
      {/* Product floating to cart animation */}
      <div
        className={`absolute transition-all duration-1000 ease-out ${
          isAnimating
            ? 'top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 scale-100 opacity-100'
            : 'top-4 right-4 transform scale-0 opacity-0'
        }`}
      >
        <div className="bg-white rounded-lg shadow-lg p-3 border-2 border-green-500 max-w-xs">
          <div className="flex items-center space-x-3">
            {productImage && (
              <img
                src={productImage}
                alt={productName}
                className="w-12 h-12 object-cover rounded"
              />
            )}
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium text-gray-900 truncate">
                {productName || 'Product'}
              </p>
              <p className="text-xs text-green-600 font-medium">Added to cart!</p>
            </div>
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                <svg
                  className="w-5 h-5 text-white"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M5 13l4 4L19 7"
                  />
                </svg>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Floating particles effect */}
      {isAnimating && (
        <>
          {[...Array(6)].map((_, i) => (
            <div
              key={i}
              className={`absolute w-2 h-2 bg-green-400 rounded-full animate-ping`}
              style={{
                top: `${50 + Math.random() * 10 - 5}%`,
                left: `${50 + Math.random() * 10 - 5}%`,
                animationDelay: `${i * 100}ms`,
                animationDuration: '1s',
              }}
            />
          ))}
        </>
      )}
    </div>
  );
};
