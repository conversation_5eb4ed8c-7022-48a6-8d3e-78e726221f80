# 🚀 Project Setup Guide

This guide will help you set up the FullStack Next.js E-Commerce application from scratch.

## 📋 Prerequisites

Before running the setup scripts, make sure you have the following installed:

### Required Software:

- **Node.js 18+** - [Download here](https://nodejs.org/)
- **Docker Desktop** - [Download here](https://www.docker.com/products/docker-desktop)
- **Git** - [Download here](https://git-scm.com/)

### For Windows Users:

- Windows 10/11
- PowerShell or Command Prompt

### For Mac/Linux Users:

- macOS 10.15+ or Linux distribution
- Terminal with bash support

## 🎯 Quick Setup

### Option 1: Automated Setup (Recommended)

#### For Windows:

```bash
# Run the setup script
npm run setup:windows
```

#### For Mac:

```bash
# Run the setup script
npm run setup:mac
```

#### For Linux:

```bash
# Run the setup script
npm run setup:linux
```

This will automatically:

- Install all dependencies
- Set up environment variables
- Start Docker containers (PostgreSQL + Adminer)
- Set up database schema
- Generate Prisma client
- Seed database with test data
- Install Playwright browsers

### Option 2: Manual Setup

If you prefer to run the setup manually:

```bash
# 1. Install dependencies
npm install

# 2. Copy environment file
cp .env.example .env.local  # Mac/Linux
copy .env.example .env.local  # Windows

# 3. Start Docker containers
docker-compose up -d

# 4. Setup database
npm run db:push
npm run db:generate
npm run db:seed

# 5. Install Playwright browsers (optional)
npx playwright install
```

## 🗄️ Database Management

### Clear and Seed Database

#### For Windows:

```bash
npm run db:clear-seed:windows
```

#### For Mac/Linux:

```bash
npm run db:clear-seed:mac
```

### Individual Database Commands:

```bash
# Generate Prisma client
npm run db:generate

# Push schema to database
npm run db:push

# Seed database with test data
npm run db:seed

# Reset database (clears all data)
npm run db:reset

# Open Prisma Studio
npm run db:studio
```

## 🎭 Test Accounts

After running the setup, you'll have these test accounts available:

| Role       | Email              | Password    | Permissions                 |
| ---------- | ------------------ | ----------- | --------------------------- |
| **Admin**  | <EMAIL>  | password123 | Full access to all features |
| **Member** | <EMAIL> | password123 | Can shop and view orders    |
| **User**   | <EMAIL>   | password123 | Can only browse products    |

## 🔧 Available Scripts

### Setup Scripts:

- `npm run setup:windows` - Complete project setup for Windows
- `npm run setup:mac` - Complete project setup for Mac
- `npm run setup:linux` - Complete project setup for Linux

### Database Scripts:

- `npm run db:clear-seed:windows` - Clear and seed database (Windows)
- `npm run db:clear-seed:mac` - Clear and seed database (Mac/Linux)
- `npm run db:generate` - Generate Prisma client
- `npm run db:push` - Push schema to database
- `npm run db:seed` - Seed database with test data
- `npm run db:studio` - Open Prisma Studio
- `npm run db:reset` - Reset database

### Development Scripts:

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint
- `npm run format` - Format code with Prettier

### Testing Scripts:

- `npm run test:e2e` - Run end-to-end tests
- `npm run test:e2e:ui` - Run tests with UI mode
- `npm run test:all` - Run all tests and checks

## 🌐 Application URLs

After setup, these services will be available:

- **Main Application**: http://localhost:3000
- **Database Admin (Adminer)**: http://localhost:8080
- **Prisma Studio**: Run `npm run db:studio`

## 🐳 Docker Services

The setup includes these Docker containers:

- **PostgreSQL Database**: Port 5432
- **Adminer (DB Admin)**: Port 8080

### Docker Commands:

```bash
# Start containers
docker-compose up -d

# Stop containers
docker-compose down

# View logs
docker-compose logs

# Restart containers
docker-compose restart
```

## 🔍 Troubleshooting

### Common Issues:

#### 1. "Docker is not running"

**Solution**: Start Docker Desktop and wait for it to fully load.

#### 2. "Port already in use"

**Solution**: Stop other services using ports 3000, 5432, or 8080:

```bash
# Check what's using the port
netstat -ano | findstr :3000  # Windows
lsof -i :3000                 # Mac/Linux

# Kill the process if needed
taskkill /PID <PID> /F        # Windows
kill -9 <PID>                 # Mac/Linux
```

#### 3. "Database connection failed"

**Solution**:

- Make sure Docker containers are running: `docker-compose ps`
- Check database logs: `docker-compose logs postgres`
- Verify connection string in `.env.local`

#### 4. "Prisma client not generated"

**Solution**: Run `npm run db:generate`

#### 5. "Permission denied" (Mac/Linux)

**Solution**: Make scripts executable:

```bash
chmod +x scripts/*.sh
```

### Reset Everything:

If you encounter persistent issues, you can reset everything:

```bash
# Stop all containers
docker-compose down -v

# Remove node_modules
rm -rf node_modules  # Mac/Linux
rmdir /s node_modules  # Windows

# Remove .next build cache
rm -rf .next  # Mac/Linux
rmdir /s .next  # Windows

# Start fresh
npm install
npm run setup:windows  # or setup:mac/setup:linux
```

## 📚 Next Steps

After successful setup:

1. **Start Development Server**:

   ```bash
   npm run dev
   ```

2. **Open Application**: Visit http://localhost:3000

3. **Login**: Use one of the test accounts above

4. **Explore Features**:

   - Browse products as any user
   - Add to cart as member/admin
   - Manage products as admin
   - View transactions as admin

5. **Run Tests**:
   ```bash
   npm run test:e2e
   ```

## 🆘 Getting Help

If you encounter issues:

1. Check the troubleshooting section above
2. Verify all prerequisites are installed
3. Make sure Docker is running
4. Check the console for error messages
5. Try the reset procedure if needed

## 🎉 Success!

If everything is working correctly, you should see:

- ✅ Application running at http://localhost:3000
- ✅ Database accessible via Adminer at http://localhost:8080
- ✅ Test accounts working
- ✅ Sample products loaded
- ✅ All tests passing

Happy coding! 🚀
